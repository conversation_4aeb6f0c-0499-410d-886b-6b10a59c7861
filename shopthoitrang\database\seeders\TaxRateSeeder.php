<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TaxRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $taxRates = [
            [
                'name' => 'VAT Việt Nam',
                'code' => 'VAT_VN',
                'rate' => 0.1000, // 10%
                'type' => 'percentage',
                'country' => 'VN',
                'is_active' => true,
                'is_default' => true,
                'description' => 'Thuế giá trị gia tăng áp dụng tại Việt Nam'
            ],
            [
                'name' => 'Thuế Nhập Khẩu',
                'code' => 'IMPORT_VN',
                'rate' => 0.0500, // 5%
                'type' => 'percentage',
                'country' => 'VN',
                'is_active' => true,
                'is_default' => false,
                'description' => 'Thuế nhập khẩu cho hàng hóa từ nước ngoài'
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'code' => 'TAX_FREE',
                'rate' => 0.0000, // 0%
                'type' => 'percentage',
                'country' => 'VN',
                'is_active' => true,
                'is_default' => false,
                'description' => 'Áp dụng cho các sản phẩm được miễn thuế'
            ]
        ];

        foreach ($taxRates as $taxRate) {
            DB::table('tax_rates')->updateOrInsert(
                ['code' => $taxRate['code']],
                array_merge($taxRate, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
