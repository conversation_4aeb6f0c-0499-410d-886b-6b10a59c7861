<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\Manufacturer;
use App\Models\User;
use App\Models\Order;
use App\Models\ProductReview;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Add admin middleware when created
        // $this->middleware('admin');
    }

    public function dashboard()
    {
        // Get statistics for dashboard
        $stats = $this->getDashboardStats();
        $recentOrders = $this->getRecentOrders();
        $topProducts = $this->getTopProducts();
        $salesChart = $this->getSalesChartData();

        return view('admin.dashboard', compact('stats', 'recentOrders', 'topProducts', 'salesChart'));
    }

    private function getDashboardStats()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_products' => Product::count(),
            'active_products' => Product::where('status', 'active')->count(),
            'total_categories' => Category::where('is_active', true)->count(),
            'total_manufacturers' => Manufacturer::where('is_active', true)->count(),
            'total_users' => User::count(),
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'completed_orders' => Order::where('status', 'delivered')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_order'),
            'monthly_revenue' => Order::where('payment_status', 'paid')
                                    ->where('created_at', '>=', $thisMonth)
                                    ->sum('total_order'),
            'daily_orders' => Order::whereDate('created_at', $today)->count(),
            'pending_reviews' => ProductReview::where('status', 'pending')->count(),
            'low_stock_products' => Product::whereColumn('quantity_product', '<=', 'min_stock_level')->count(),
        ];
    }

    private function getRecentOrders($limit = 10)
    {
        return Order::with(['user'])
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }

    private function getTopProducts($limit = 10)
    {
        return Product::with(['category', 'manufacturer'])
                     ->where('status', 'active')
                     ->orderBy('view_count', 'desc')
                     ->limit($limit)
                     ->get();
    }

    private function getSalesChartData()
    {
        $last30Days = collect();

        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $sales = Order::whereDate('created_at', $date)
                         ->where('payment_status', 'paid')
                         ->sum('total_order');

            $last30Days->push([
                'date' => $date->format('Y-m-d'),
                'sales' => $sales,
                'orders' => Order::whereDate('created_at', $date)->count()
            ]);
        }

        return $last30Days;
    }

    public function settings()
    {
        $settings = DB::table('settings')->get()->groupBy('group');
        return view('admin.settings.index', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $settings = $request->except('_token', '_method');

        foreach ($settings as $key => $value) {
            DB::table('settings')
              ->where('key', $key)
              ->update(['value' => $value, 'updated_at' => now()]);
        }

        return redirect()->back()->with('success', 'Cài đặt đã được cập nhật thành công!');
    }
}
