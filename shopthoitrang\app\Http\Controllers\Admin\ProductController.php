<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\Manufacturer;
use App\Models\ProductImage;
use App\Models\ProductVariant;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of products with advanced filtering
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'manufacturer', 'images'])
                       ->withCount('reviews');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name_product', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('describe_product', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('id_category', $request->category_id);
        }

        // Filter by manufacturer
        if ($request->filled('manufacturer_id')) {
            $query->where('id_manufacturer', $request->manufacturer_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by featured
        if ($request->filled('featured')) {
            $query->where('is_featured', $request->featured === 'yes');
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'in_stock':
                    $query->where('quantity_product', '>', 0);
                    break;
                case 'low_stock':
                    $query->whereColumn('quantity_product', '<=', 'min_stock_level');
                    break;
                case 'out_of_stock':
                    $query->where('quantity_product', 0);
                    break;
            }
        }

        // Price range filter
        if ($request->filled('price_min')) {
            $query->where('price_product', '>=', $request->price_min);
        }
        if ($request->filled('price_max')) {
            $query->where('price_product', '<=', $request->price_max);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSorts = ['name_product', 'price_product', 'quantity_product', 'created_at', 'view_count'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(15);

        // Get filter options
        $categories = Category::active()->orderBy('name_category')->get();
        $manufacturers = Manufacturer::active()->orderBy('name_manufacturer')->get();

        return view('admin.products.index', compact('products', 'categories', 'manufacturers'));
    }

    /**
     * Show the form for creating a new product
     */
    public function create()
    {
        $categories = Category::active()->orderBy('name_category')->get();
        $manufacturers = Manufacturer::active()->orderBy('name_manufacturer')->get();
        $attributes = ProductAttribute::orderBy('name')->get();

        return view('admin.products.create', compact('categories', 'manufacturers', 'attributes'));
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request)
    {
        $request->validate([
            'name_product' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:products,slug',
            'id_category' => 'required|exists:categories,id_category',
            'id_manufacturer' => 'required|exists:manufacturers,id_manufacturer',
            'status' => 'required|in:active,inactive,draft',
            'price_product' => 'required|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0|gt:price_product',
            'cost_price' => 'nullable|numeric|min:0',
            'quantity_product' => 'required|integer|min:0',
            'min_stock_level' => 'nullable|integer|min:0',
            'max_stock_level' => 'nullable|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'describe_product' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'tags' => 'nullable|string',
            'is_featured' => 'boolean',
            'is_digital' => 'boolean',
            'track_inventory' => 'boolean',
            'allow_backorder' => 'boolean',
            'requires_shipping' => 'boolean',
            'featured_until' => 'nullable|date|after:today',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120'
        ], [
            'name_product.required' => 'Tên sản phẩm là bắt buộc',
            'slug.required' => 'Slug là bắt buộc',
            'slug.unique' => 'Slug đã tồn tại',
            'id_category.required' => 'Danh mục là bắt buộc',
            'id_manufacturer.required' => 'Thương hiệu là bắt buộc',
            'price_product.required' => 'Giá sản phẩm là bắt buộc',
            'compare_price.gt' => 'Giá so sánh phải lớn hơn giá bán',
            'describe_product.required' => 'Mô tả sản phẩm là bắt buộc',
            'images.*.image' => 'File phải là hình ảnh',
            'images.*.max' => 'Hình ảnh không được vượt quá 5MB'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->only([
                'name_product', 'slug', 'id_category', 'id_manufacturer', 'status',
                'price_product', 'compare_price', 'cost_price', 'quantity_product',
                'min_stock_level', 'max_stock_level', 'weight', 'length', 'width', 'height',
                'describe_product', 'short_description', 'meta_title', 'meta_description',
                'meta_keywords', 'featured_until'
            ]);

            $data['is_featured'] = $request->boolean('is_featured');
            $data['is_digital'] = $request->boolean('is_digital');
            $data['track_inventory'] = $request->boolean('track_inventory', true);
            $data['allow_backorder'] = $request->boolean('allow_backorder');
            $data['requires_shipping'] = $request->boolean('requires_shipping', true);
            $data['has_variants'] = false; // Will be updated if variants are added
            $data['view_count'] = 0;
            $data['average_rating'] = 0;
            $data['review_count'] = 0;

            // Process tags
            if ($request->filled('tags')) {
                $data['tags'] = array_map('trim', explode(',', $request->tags));
            }

            $product = Product::create($data);

            // Handle image uploads
            if ($request->hasFile('images')) {
                $this->handleImageUploads($product, $request->file('images'));
            }

            DB::commit();

            return redirect()->route('admin.products.index')
                           ->with('success', 'Sản phẩm đã được tạo thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        $product->load(['category', 'manufacturer', 'images', 'reviews.user', 'attributes']);
        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit(Product $product)
    {
        $categories = Category::active()->orderBy('name_category')->get();
        $manufacturers = Manufacturer::active()->orderBy('name_manufacturer')->get();
        $attributes = ProductAttribute::orderBy('name')->get();

        $product->load(['images', 'attributes']);

        return view('admin.products.edit', compact('product', 'categories', 'manufacturers', 'attributes'));
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name_product' => 'required|string|max:255',
            'slug' => [
                'required',
                'string',
                'max:255',
                Rule::unique('products', 'slug')->ignore($product->id_product, 'id_product')
            ],
            'id_category' => 'required|exists:categories,id_category',
            'id_manufacturer' => 'required|exists:manufacturers,id_manufacturer',
            'status' => 'required|in:active,inactive,draft',
            'price_product' => 'required|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0|gt:price_product',
            'cost_price' => 'nullable|numeric|min:0',
            'quantity_product' => 'required|integer|min:0',
            'describe_product' => 'required|string',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->only([
                'name_product', 'slug', 'id_category', 'id_manufacturer', 'status',
                'price_product', 'compare_price', 'cost_price', 'quantity_product',
                'min_stock_level', 'max_stock_level', 'weight', 'length', 'width', 'height',
                'describe_product', 'short_description', 'meta_title', 'meta_description',
                'meta_keywords', 'featured_until'
            ]);

            $data['is_featured'] = $request->boolean('is_featured');
            $data['is_digital'] = $request->boolean('is_digital');
            $data['track_inventory'] = $request->boolean('track_inventory', true);
            $data['allow_backorder'] = $request->boolean('allow_backorder');
            $data['requires_shipping'] = $request->boolean('requires_shipping', true);

            // Process tags
            if ($request->filled('tags')) {
                $data['tags'] = array_map('trim', explode(',', $request->tags));
            }

            $product->update($data);

            // Handle new image uploads
            if ($request->hasFile('images')) {
                $this->handleImageUploads($product, $request->file('images'));
            }

            DB::commit();

            return redirect()->route('admin.products.index')
                           ->with('success', 'Sản phẩm đã được cập nhật thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified product
     */
    public function destroy(Product $product)
    {
        try {
            DB::beginTransaction();

            // Delete product images
            foreach ($product->images as $image) {
                $this->deleteImage($image->image_path);
                $image->delete();
            }

            // Delete product attributes
            $product->attributes()->detach();

            $product->delete();

            DB::commit();

            return redirect()->route('admin.products.index')
                           ->with('success', 'Sản phẩm đã được xóa thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Generate slug from product name
     */
    public function generateSlug(Request $request)
    {
        $name = $request->input('name');
        $slug = Str::slug($name);

        // Check if slug exists and make it unique
        $originalSlug = $slug;
        $counter = 1;

        while (Product::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return response()->json(['slug' => $slug]);
    }

    /**
     * Toggle product status
     */
    public function toggleStatus(Product $product)
    {
        $product->update(['status' => $product->status === 'active' ? 'inactive' : 'active']);

        $status = $product->status === 'active' ? 'kích hoạt' : 'vô hiệu hóa';
        return response()->json([
            'success' => true,
            'message' => "Sản phẩm đã được {$status}",
            'status' => $product->status
        ]);
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(Product $product)
    {
        $product->update(['is_featured' => !$product->is_featured]);

        $status = $product->is_featured ? 'đánh dấu nổi bật' : 'bỏ đánh dấu nổi bật';
        return response()->json([
            'success' => true,
            'message' => "Sản phẩm đã được {$status}",
            'featured' => $product->is_featured
        ]);
    }

    /**
     * Handle image uploads
     */
    private function handleImageUploads(Product $product, array $images)
    {
        $isFirst = $product->images()->count() === 0;

        foreach ($images as $index => $image) {
            $filename = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('public/products', $filename);

            ProductImage::create([
                'product_id' => $product->id_product,
                'image_path' => str_replace('public/', '', $path),
                'alt_text' => $product->name_product,
                'sort_order' => $index,
                'is_primary' => $isFirst && $index === 0,
                'image_type' => 'gallery'
            ]);
        }
    }

    /**
     * Delete image file
     */
    private function deleteImage($imagePath)
    {
        if (Storage::exists("public/{$imagePath}")) {
            Storage::delete("public/{$imagePath}");
        }
    }

    // Legacy methods for backward compatibility
    public function indexProduct(Request $request) {
        try {
            $page = $request->query('page', 1);
            $search = $request->get('search');

            // Validate page parameter
            if (!is_numeric($page) || $page < 1) {
                return redirect()->route('product.listproduct')
                    ->with('error', 'Tham số trang không hợp lệ');
            }

            // Get products with search functionality
            if ($search) {
                // Tìm kiếm sản phẩm theo tên, sắp xếp theo mới nhất
                $products = Product::where('name_product', 'LIKE', '%' . $search . '%')
                                  ->orderBy('created_at', 'desc')
                                  ->orderBy('id_product', 'desc')
                                  ->paginate(6);

                // Get total for search results
                $totalProducts = Product::where('name_product', 'LIKE', '%' . $search . '%')->count();
            } else {
                // Get total number of products first
                $totalProducts = Product::count();
                $perPage = 6;
                $lastPage = ceil($totalProducts / $perPage);

                // Check if requested page exceeds total pages
                if ($page > $lastPage && $lastPage > 0) {
                    return redirect()->route('product.listproduct')
                        ->with('error', 'Trang không tồn tại');
                }

                // Lấy tất cả sản phẩm với phân trang, sắp xếp theo mới nhất
                $products = Product::getProductsWithPagination(6);
            }

            $category = Category::all();
            $manufacturer = Manufacturer::getAllManufacturers();
            return view('admin.product.listproduct', [
                'products' => $products,
                'categorys' => $category,
                'manufacturers' => $manufacturer,
                'search' => $search
            ]);
        } catch (\Exception $e) {
            return redirect()->route('product.listproduct')
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function indexAddProduct(){
        $category = Category::all();
        $manufacturer = Manufacturer::getAllManufacturers();
        return view('admin.product.addproduct', [
            'categorys' => $category,
            'manufacturers' => $manufacturer
        ]);
    }
    
    private function validateInput($data) {
        // Check for whitespace-only input
        $textFields = ['name_product', 'describe_product', 'specifications'];
        foreach ($textFields as $field) {
            if (isset($data[$field]) && trim($data[$field]) === '') {
                return false;
            }
        }

        // Check for full-width characters
        $fullWidthPattern = '/[\x{3000}-\x{303F}\x{FF00}-\x{FFEF}]/u';
        foreach ($textFields as $field) {
            if (isset($data[$field]) && preg_match($fullWidthPattern, $data[$field])) {
                return false;
            }
        }

        return true;
    }

    private function validateImage($file) {
        // Check file extension
        $allowedExtensions = ['jpeg', 'png', 'jpg', 'gif'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedExtensions)) {
            return false;
        }

        // Check file size (2MB max)
        if ($file->getSize() > 2 * 1024 * 1024) {
            return false;
        }

        // Check if file is actually an image
        if (!getimagesize($file->getPathname())) {
            return false;
        }

        return true;
    }

    public function addProduct(Request $request){
        // Validate the request
        $validator = Validator::make($request->all(), Product::$rules, Product::$messages);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Validate whitespace and full-width characters
        if (!$this->validateInput($data)) {
            return redirect()->back()
                ->with('error', 'Không được phép nhập toàn khoảng trắng hoặc ký tự full-width')
                ->withInput();
        }

        // Handle image upload
        if($request->hasFile('image_address_product')) {
            $file = $request->file('image_address_product');
            
            // Validate image
            if (!$this->validateImage($file)) {
                return redirect()->back()
                    ->with('error', 'File không phải là hình ảnh hợp lệ hoặc kích thước quá lớn')
                    ->withInput();
            }

            $ex = $file->getClientOriginalExtension();
            $filename = time().'.'.$ex;
            $file->move('uploads/productimage/',$filename);
            $data['image_address_product'] = $filename;
        }

        try {
            // Create new product
            Product::createProduct([
                'selected_category' => $data['id_category'],
                'selected_manufacturer' => $data['id_manufacturer'],
                'name_product' => $data['name_product'],
                'quantity_product' => $data['quantity_product'],
                'price_product' => $data['price_product'],
                'image_address_product' => $data['image_address_product'],
                'describe_product' => $data['describe_product'],
                'specifications' => $data['specifications'] ?? null,
                'sizes' => $data['sizes'] ?? null,
                'colors' => $data['colors'] ?? null,
            ]);

            return redirect()->route('product.listproduct')
                ->with('success', 'Thêm sản phẩm thành công');
        } catch (\Exception $e) {
            // If image was uploaded but product creation failed, delete the image
            if (isset($data['image_address_product'])) {
                $image_path = 'uploads/productimage/' . $data['image_address_product'];
                if (File::exists($image_path)) {
                    File::delete($image_path);
                }
            }

            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra khi thêm sản phẩm: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function deleteProduct(Request $request){
        try {
            $product = Product::findProductById($request->get('id'));
            
            if (!$product) {
                return redirect()->back()
                    ->with('error', 'Không tìm thấy sản phẩm để xóa');
            }

            // Delete the product image if it exists
            if ($product->image_address_product) {
                $image_path = 'uploads/productimage/' . $product->image_address_product;
                if (File::exists($image_path)) {
                    File::delete($image_path);
                }
            }

            // Use transaction to ensure atomicity
            DB::beginTransaction();
            try {
                Product::destroy($request->get('id'));
                DB::commit();
                return redirect()->route('product.listproduct')
                    ->with('success', 'Xóa sản phẩm thành công');
            } catch (\Exception $e) {
                DB::rollBack();
                return redirect()->back()
                    ->with('error', 'Có lỗi xảy ra khi xóa sản phẩm: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra khi xóa sản phẩm: ' . $e->getMessage());
        }
    }

    public function indexUpdateProduct(Request $request){
        try {
            $id = $request->get('id');
            
            // Validate ID format
            if (!is_numeric($id)) {
                return redirect()->route('product.listproduct')
                    ->with('error', 'ID sản phẩm không hợp lệ');
            }

            $product = Product::findProductById($id);
            $category = Category::all();
            $manufacturer = Manufacturer::getAllManufacturers();
            
            if (!$product) {
                return redirect()->route('product.listproduct')
                    ->with('error', 'Không tìm thấy sản phẩm với ID: ' . $id);
            }

            return view('admin.product.updateproduct', [
                'products' => $product,
                'categorys' => $category,
                'manufacturers' => $manufacturer
            ]);
        } catch (\Exception $e) {
            return redirect()->route('product.listproduct')
                ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }
    
    public function updateProduct(Request $request){
        // Define validation rules specifically for update, making image optional
        $updateRules = Product::$rules;
        $updateRules['image_address_product'] = 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048';

        // Sử dụng rules giống như khi thêm mới
        $validator = Validator::make($request->all(), $updateRules, Product::$messages);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Validate whitespace and full-width characters
        if (!$this->validateInput($data)) {
            return redirect()->back()
                ->with('error', 'Không được phép nhập toàn khoảng trắng hoặc ký tự full-width')
                ->withInput();
        }
        
        try {
            DB::beginTransaction();
            
            $product = Product::findProductById($data['id']);
            if (!$product) {
                DB::rollBack();
                return redirect()->back()
                    ->with('error', 'Không tìm thấy sản phẩm để cập nhật. Có thể sản phẩm đã bị xóa ở nơi khác.')
                    ->withInput();
            }

            // Check if product was modified by another user
            if ($product->updated_at != $request->input('updated_at')) {
                DB::rollBack();
                return redirect()->back()
                    ->with('error', 'Sản phẩm đã được cập nhật bởi người dùng khác. Vui lòng tải lại trang và thử lại.')
                    ->withInput();
            }

            // Handle image update
            if($request->hasFile('image_address_product')) {
                $file = $request->file('image_address_product');
                
                // Validate image
                if (!$this->validateImage($file)) {
                    DB::rollBack();
                    return redirect()->back()
                        ->with('error', 'File không phải là hình ảnh hợp lệ hoặc kích thước quá lớn')
                        ->withInput();
                }

                // Xóa ảnh cũ
                $image_cu = 'uploads/productimage/' . $product->image_address_product;
                if(File::exists($image_cu)) {
                    File::delete($image_cu);
                }
                
                // Upload ảnh mới
                $ex = $file->getClientOriginalExtension();
                $filename = time().'.'.$ex;
                $file->move('uploads/productimage/',$filename);
                $data['image_address_product'] = $filename;
            } else {
                // Keep existing image if no new image is uploaded
                $data['image_address_product'] = $product->image_address_product;
            }

            // Update product
            Product::updateProductById($data['id'], [
                'selected_category' => $data['id_category'],
                'selected_manufacturer' => $data['id_manufacturer'],
                'name_product' => $data['name_product'],
                'quantity_product' => $data['quantity_product'],
                'price_product' => $data['price_product'],
                'describe_product' => $data['describe_product'],
                'specifications' => $data['specifications'] ?? null,
                'sizes' => $data['sizes'] ?? null,
                'colors' => $data['colors'] ?? null,
                'image_address_product' => $data['image_address_product'],
            ]);

            DB::commit();
            return redirect()->route('product.listproduct')
                ->with('success', 'Cập nhật sản phẩm thành công');
        } catch (\Exception $e) {
            DB::rollBack();
            // Nếu upload ảnh mới mà lỗi thì xóa ảnh mới
            if (isset($data['image_address_product']) && $request->hasFile('image_address_product')) {
                $image_path = 'uploads/productimage/' . $data['image_address_product'];
                if (File::exists($image_path)) {
                    File::delete($image_path);
                }
            }

            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra khi cập nhật sản phẩm: ' . $e->getMessage())
                ->withInput();
        }
    }
}