<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('redirects', function (Blueprint $table) {
            $table->id();
            $table->string('from_url'); // Old URL
            $table->string('to_url'); // New URL
            $table->integer('status_code')->default(301); // 301, 302, etc.
            $table->boolean('is_active')->default(true);
            $table->integer('hit_count')->default(0); // Track usage
            $table->text('description')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['from_url', 'is_active']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('redirects');
    }
};
