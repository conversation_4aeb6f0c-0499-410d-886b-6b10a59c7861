@extends('admin.layouts.simple')

@section('title', 'Products')
@section('page_title', 'Products Management')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Products List</h3>
                    <div class="card-tools">
                        <a href="#" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Product
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Manufacturer</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($products as $product)
                                <tr>
                                    <td>{{ $product->id_product }}</td>
                                    <td>
                                        @if($product->image_address_product)
                                            <img src="{{ asset('uploads/productimage/' . $product->image_address_product) }}" 
                                                 alt="{{ $product->name_product }}" 
                                                 style="width: 50px; height: 50px; object-fit: cover;" class="img-thumbnail">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ Str::limit($product->name_product, 30) }}</strong>
                                        @if($product->slug)
                                            <br><small class="text-muted">{{ $product->slug }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $product->category->name_category ?? 'N/A' }}</td>
                                    <td>{{ $product->manufacturer->name_manufacturer ?? 'N/A' }}</td>
                                    <td>
                                        <strong>{{ number_format($product->price_product, 0, ',', '.') }}₫</strong>
                                        @if($product->compare_price && $product->compare_price > $product->price_product)
                                            <br><small class="text-muted"><del>{{ number_format($product->compare_price, 0, ',', '.') }}₫</del></small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $product->quantity_product > 0 ? 'success' : 'danger' }}">
                                            {{ $product->quantity_product }}
                                        </span>
                                    </td>
                                    <td>
                                        @if(($product->status ?? 'active') === 'active')
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info">View</a>
                                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                                        <a href="#" class="btn btn-sm btn-danger">Delete</a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center">No products found</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($products->hasPages())
                <div class="card-footer">
                    {{ $products->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
@endsection
