<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_rates', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // VAT, GST, Sales Tax
            $table->string('code')->unique(); // VAT_VN, GST_AU
            $table->decimal('rate', 5, 4); // 0.1000 for 10%
            $table->enum('type', ['percentage', 'fixed'])->default('percentage');
            $table->string('country', 2); // Country code
            $table->string('state_province')->nullable();
            $table->string('city')->nullable();
            $table->string('postal_code')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->date('effective_from')->nullable();
            $table->date('effective_to')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['country', 'is_active']);
            $table->index(['is_active', 'is_default']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_rates');
    }
};
