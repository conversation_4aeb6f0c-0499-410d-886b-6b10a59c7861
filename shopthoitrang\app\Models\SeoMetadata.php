<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SeoMetadata extends Model
{
    use HasFactory;

    protected $fillable = [
        'seoable_type',
        'seoable_id',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
        'twitter_card',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'schema_markup',
        'noindex',
        'nofollow',
        'robots',
        'focus_keyword',
        'seo_score'
    ];

    protected $casts = [
        'schema_markup' => 'array',
        'noindex' => 'boolean',
        'nofollow' => 'boolean',
        'seo_score' => 'integer'
    ];

    // Polymorphic relationship
    public function seoable()
    {
        return $this->morphTo();
    }

    // Helper methods
    public function getMetaTitleOrDefault()
    {
        return $this->meta_title ?: $this->seoable->name ?? 'Untitled';
    }

    public function getMetaDescriptionOrDefault()
    {
        return $this->meta_description ?: $this->seoable->description ?? '';
    }

    public function getRobotsContent()
    {
        $robots = [];

        if ($this->noindex) {
            $robots[] = 'noindex';
        } else {
            $robots[] = 'index';
        }

        if ($this->nofollow) {
            $robots[] = 'nofollow';
        } else {
            $robots[] = 'follow';
        }

        if ($this->robots) {
            $robots[] = $this->robots;
        }

        return implode(', ', $robots);
    }
}
