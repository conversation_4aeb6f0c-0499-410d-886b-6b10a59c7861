<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Standard, Express, Overnight
            $table->string('code')->unique(); // standard, express, overnight
            $table->text('description')->nullable();

            // Pricing
            $table->decimal('base_cost', 10, 2)->default(0);
            $table->decimal('cost_per_kg', 10, 2)->default(0);
            $table->decimal('free_shipping_threshold', 10, 2)->nullable();

            // Delivery time
            $table->integer('min_delivery_days')->default(1);
            $table->integer('max_delivery_days')->default(7);

            // Availability
            $table->json('available_countries')->nullable(); // ["VN", "US", "JP"]
            $table->json('available_regions')->nullable(); // For specific regions within countries

            // Settings
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_signature')->default(false);
            $table->boolean('insurance_available')->default(false);
            $table->decimal('max_weight', 8, 2)->nullable(); // Maximum weight limit
            $table->integer('sort_order')->default(0);

            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_methods');
    }
};
