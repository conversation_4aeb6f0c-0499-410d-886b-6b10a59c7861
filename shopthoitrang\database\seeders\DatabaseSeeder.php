<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            // Core system data
            SettingsSeeder::class,
            TaxRateSeeder::class,
            ShippingMethodSeeder::class,

            // User management
            UserSeeder::class,

            // Product catalog
            CategorySeeder::class,
            ManufacturerSeeder::class,
            ProductAttributeSeeder::class,
            ProductSeeder::class,
            ProductVariantSeeder::class,
            ProductImageSeeder::class,
            ProductAttributeValueSeeder::class,

            // Content management
            BlogCategorySeeder::class,
            PostSeeder::class,

            // Marketing
            CouponSeeder::class,

            // Sample orders and reviews
            OrderSeeder::class,
            ProductReviewSeeder::class,

            // SEO data
            SeoMetadataSeeder::class,
            SitemapSeeder::class,
        ]);
    }
}
