<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Profile information
            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            $table->string('phone')->nullable()->after('email');
            $table->date('date_of_birth')->nullable()->after('phone');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('date_of_birth');
            $table->text('avatar')->nullable()->after('gender');

            // Account status and verification
            $table->boolean('is_active')->default(true)->after('avatar');
            $table->boolean('phone_verified')->default(false)->after('is_active');
            $table->timestamp('phone_verified_at')->nullable()->after('phone_verified');

            // Social login
            $table->string('google_id')->nullable()->after('phone_verified_at');
            $table->string('facebook_id')->nullable()->after('google_id');

            // User preferences
            $table->string('preferred_language', 5)->default('vi')->after('facebook_id');
            $table->string('preferred_currency', 3)->default('VND')->after('preferred_language');
            $table->boolean('newsletter_subscription')->default(false)->after('preferred_currency');
            $table->boolean('sms_notifications')->default(false)->after('newsletter_subscription');

            // Customer type and loyalty
            $table->enum('user_type', ['customer', 'admin', 'staff', 'vendor'])->default('customer')->after('sms_notifications');
            $table->integer('loyalty_points')->default(0)->after('user_type');
            $table->timestamp('last_login_at')->nullable()->after('loyalty_points');

            // SEO and tracking
            $table->string('referral_code', 20)->unique()->nullable()->after('last_login_at');
            $table->string('referred_by')->nullable()->after('referral_code');

            // Soft delete
            $table->softDeletes()->after('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'first_name', 'last_name', 'phone', 'date_of_birth', 'gender', 'avatar',
                'is_active', 'phone_verified', 'phone_verified_at', 'google_id', 'facebook_id',
                'preferred_language', 'preferred_currency', 'newsletter_subscription', 'sms_notifications',
                'user_type', 'loyalty_points', 'last_login_at', 'referral_code', 'referred_by'
            ]);
            $table->dropSoftDeletes();
        });
    }
};
