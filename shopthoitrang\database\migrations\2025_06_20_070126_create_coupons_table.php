<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();

            // Discount settings
            $table->enum('type', ['fixed', 'percentage']); // Fixed amount or percentage
            $table->decimal('value', 10, 2); // Discount value
            $table->decimal('minimum_amount', 10, 2)->nullable(); // Minimum order amount
            $table->decimal('maximum_discount', 10, 2)->nullable(); // Maximum discount for percentage type

            // Usage limits
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_limit_per_user')->nullable(); // Per user usage limit
            $table->integer('used_count')->default(0); // Current usage count

            // Validity period
            $table->timestamp('starts_at');
            $table->timestamp('expires_at');

            // Conditions
            $table->json('applicable_categories')->nullable(); // Category IDs
            $table->json('applicable_products')->nullable(); // Product IDs
            $table->json('applicable_brands')->nullable(); // Brand IDs
            $table->json('excluded_categories')->nullable();
            $table->json('excluded_products')->nullable();
            $table->json('excluded_brands')->nullable();

            // User restrictions
            $table->json('applicable_users')->nullable(); // Specific user IDs
            $table->json('applicable_user_groups')->nullable(); // User groups/roles
            $table->boolean('first_order_only')->default(false);

            // Settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_public')->default(true); // Public or private coupon
            $table->boolean('free_shipping')->default(false);

            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'starts_at', 'expires_at']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupons');
    }
};
