@extends('admin.layouts.app')

@section('title', 'Quản lý Dan<PERSON> mục')
@section('page_title', 'Quản lý Danh mục')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active"><PERSON><PERSON> mục</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list mr-1"></i>
                        Danh sách danh mục
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.categories.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Thêm danh mục mới
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ route('admin.categories.index') }}" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search">Tìm kiếm:</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="{{ request('search') }}" placeholder="Tên danh mục, slug...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">Trạng thái:</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Hoạt động</option>
                                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="parent_id">Danh mục cha:</label>
                                <select name="parent_id" id="parent_id" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="root" {{ request('parent_id') === 'root' ? 'selected' : '' }}>Danh mục gốc</option>
                                    @foreach($parentCategories as $parent)
                                        <option value="{{ $parent->id_category }}" {{ request('parent_id') == $parent->id_category ? 'selected' : '' }}>
                                            {{ $parent->name_category }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-info mr-2">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 50px">#</th>
                                    <th>Hình ảnh</th>
                                    <th>Tên danh mục</th>
                                    <th>Slug</th>
                                    <th>Danh mục cha</th>
                                    <th>Sản phẩm</th>
                                    <th>Thứ tự</th>
                                    <th>Trạng thái</th>
                                    <th>Nổi bật</th>
                                    <th style="width: 150px">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($categories as $category)
                                <tr>
                                    <td>{{ $category->id_category }}</td>
                                    <td>
                                        @if($category->image)
                                            <img src="{{ asset('storage/' . $category->image) }}" 
                                                 alt="{{ $category->name_category }}" 
                                                 class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; border-radius: 4px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($category->icon)
                                                <i class="{{ $category->icon }} mr-2"></i>
                                            @endif
                                            <div>
                                                <strong>{{ $category->name_category }}</strong>
                                                @if($category->description)
                                                    <br><small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code>{{ $category->slug }}</code>
                                    </td>
                                    <td>
                                        @if($category->parent)
                                            <span class="badge badge-info">{{ $category->parent->name_category }}</span>
                                        @else
                                            <span class="badge badge-secondary">Danh mục gốc</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ $category->active_products_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-light">{{ $category->sort_order ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input status-toggle" 
                                                   id="status-{{ $category->id_category }}"
                                                   data-id="{{ $category->id_category }}"
                                                   {{ $category->is_active ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="status-{{ $category->id_category }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        @if($category->featured)
                                            <i class="fas fa-star text-warning" title="Nổi bật"></i>
                                        @else
                                            <i class="far fa-star text-muted" title="Không nổi bật"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.categories.show', $category->id_category) }}" 
                                               class="btn btn-info btn-sm" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.categories.edit', $category->id_category) }}" 
                                               class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" 
                                                    onclick="confirmDelete('{{ route('admin.categories.destroy', $category->id_category) }}', 'Bạn có chắc chắn muốn xóa danh mục này?')"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-folder-open fa-3x mb-3"></i>
                                            <p>Không có danh mục nào được tìm thấy.</p>
                                            <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Thêm danh mục đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                @if($categories->hasPages())
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted">
                                Hiển thị {{ $categories->firstItem() }} đến {{ $categories->lastItem() }} 
                                trong tổng số {{ $categories->total() }} danh mục
                            </small>
                        </div>
                        <div class="col-md-6">
                            {{ $categories->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle status
    $('.status-toggle').on('change', function() {
        const categoryId = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/categories/${categoryId}/toggle-status`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Có lỗi xảy ra');
                    // Revert toggle
                    $(this).prop('checked', !isActive);
                }
            },
            error: function() {
                toastr.error('Có lỗi xảy ra');
                // Revert toggle
                $(this).prop('checked', !isActive);
            }
        });
    });
});
</script>
@endpush
