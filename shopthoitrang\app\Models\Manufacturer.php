<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Manufacturer extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $primaryKey = 'id_manufacturer';

    protected $fillable = [
        'name_manufacturer',
        'slug',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'description',
        'logo',
        'banner_image',
        'website_url',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'facebook_url',
        'instagram_url',
        'twitter_url',
        'youtube_url',
        'is_active',
        'is_featured',
        'sort_order',
        'product_count'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'product_count' => 'integer'
    ];

    // Relationships
    public function products()
    {
        return $this->hasMany(Product::class, 'id_manufacturer', 'id_manufacturer');
    }

    public function activeProducts()
    {
        return $this->hasMany(Product::class, 'id_manufacturer', 'id_manufacturer')
                    ->where('status', 'active');
    }

    public function seoMetadata()
    {
        return $this->morphOne(SeoMetadata::class, 'seoable');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    // Accessors
    public function getUrlAttribute()
    {
        return route('manufacturer.show', $this->slug);
    }

    public function getLogoUrlAttribute()
    {
        return $this->logo ? asset('storage/' . $this->logo) : null;
    }

    // Helper methods
    public function updateProductCount()
    {
        $this->update(['product_count' => $this->activeProducts()->count()]);
    }

    // Legacy Query Methods (for backward compatibility)
    public static function getManufacturersWithPagination($perPage = 2)
    {
        return self::active()->paginate($perPage);
    }

    public static function getAllManufacturers()
    {
        return self::active()->get();
    }

    public static function findManufacturerById($id)
    {
        return self::findOrFail($id);
    }
}
