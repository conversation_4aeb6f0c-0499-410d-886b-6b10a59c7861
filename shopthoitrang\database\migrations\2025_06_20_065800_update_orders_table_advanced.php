<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Note: This assumes the table name is 'order' based on existing migration
        Schema::table('order', function (Blueprint $table) {
            // Order identification
            $table->string('order_number')->unique()->after('id_order');
            $table->enum('status', ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
                  ->default('pending')->after('order_number');

            // Customer information (expand existing)
            $table->string('customer_email')->after('id_user');
            $table->string('customer_phone')->nullable()->after('customer_email');
            $table->string('customer_first_name')->after('customer_phone');
            $table->string('customer_last_name')->after('customer_first_name');

            // Billing address (separate from shipping)
            $table->json('billing_address')->after('address');
            $table->json('shipping_address')->after('billing_address');

            // Order totals breakdown
            $table->decimal('subtotal', 10, 2)->after('total_order');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('subtotal');
            $table->decimal('shipping_cost', 10, 2)->default(0)->after('tax_amount');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('shipping_cost');
            $table->string('currency', 3)->default('VND')->after('discount_amount');

            // Payment information
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'])
                  ->default('pending')->after('currency');
            $table->string('payment_method')->nullable()->after('payment_status');
            $table->string('payment_reference')->nullable()->after('payment_method');

            // Shipping information
            $table->string('shipping_method')->nullable()->after('payment_reference');
            $table->string('tracking_number')->nullable()->after('shipping_method');
            $table->timestamp('shipped_at')->nullable()->after('tracking_number');
            $table->timestamp('delivered_at')->nullable()->after('shipped_at');

            // Order notes and metadata
            $table->text('notes')->nullable()->after('delivered_at');
            $table->text('admin_notes')->nullable()->after('notes');
            $table->json('metadata')->nullable()->after('admin_notes'); // For storing additional data

            // Coupon and promotion
            $table->string('coupon_code')->nullable()->after('metadata');
            $table->decimal('coupon_discount', 10, 2)->default(0)->after('coupon_code');

            // Indexes for better performance
            $table->index(['id_user', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('order_number');
            $table->index('payment_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->dropColumn([
                'order_number', 'status', 'customer_email', 'customer_phone', 'customer_first_name',
                'customer_last_name', 'billing_address', 'shipping_address', 'subtotal', 'tax_amount',
                'shipping_cost', 'discount_amount', 'currency', 'payment_status', 'payment_method',
                'payment_reference', 'shipping_method', 'tracking_number', 'shipped_at', 'delivered_at',
                'notes', 'admin_notes', 'metadata', 'coupon_code', 'coupon_discount'
            ]);
        });
    }
};
