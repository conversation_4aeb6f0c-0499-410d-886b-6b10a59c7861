@extends('admin.layouts.app')

@section('title', 'Quản lý Thương hiệu')
@section('page_title', 'Quản lý Thương hiệu')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Thương hiệu</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-industry mr-1"></i>
                        <PERSON>h sách thương hiệu
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.manufacturers.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Th<PERSON><PERSON> thương hiệu mới
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ route('admin.manufacturers.index') }}" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">Tìm kiếm:</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="{{ request('search') }}" placeholder="Tên thương hiệu, email, mô tả...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">Trạng thái:</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Hoạt động</option>
                                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="featured">Nổi bật:</label>
                                <select name="featured" id="featured" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="yes" {{ request('featured') === 'yes' ? 'selected' : '' }}>Có</option>
                                    <option value="no" {{ request('featured') === 'no' ? 'selected' : '' }}>Không</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-info mr-2">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="{{ route('admin.manufacturers.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 50px">#</th>
                                    <th>Logo</th>
                                    <th>Thương hiệu</th>
                                    <th>Liên hệ</th>
                                    <th>Website</th>
                                    <th>Sản phẩm</th>
                                    <th>Thứ tự</th>
                                    <th>Trạng thái</th>
                                    <th>Nổi bật</th>
                                    <th style="width: 150px">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($manufacturers as $manufacturer)
                                <tr>
                                    <td>{{ $manufacturer->id_manufacturer }}</td>
                                    <td>
                                        @if($manufacturer->logo)
                                            <img src="{{ asset('storage/' . $manufacturer->logo) }}" 
                                                 alt="{{ $manufacturer->name_manufacturer }}" 
                                                 class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; border-radius: 4px;">
                                                <i class="fas fa-industry text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $manufacturer->name_manufacturer }}</strong>
                                            @if($manufacturer->slug)
                                                <br><small class="text-muted">{{ $manufacturer->slug }}</small>
                                            @endif
                                            @if($manufacturer->description)
                                                <br><small class="text-muted">{{ Str::limit($manufacturer->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($manufacturer->email)
                                            <div><i class="fas fa-envelope text-muted"></i> {{ $manufacturer->email }}</div>
                                        @endif
                                        @if($manufacturer->phone)
                                            <div><i class="fas fa-phone text-muted"></i> {{ $manufacturer->phone }}</div>
                                        @endif
                                        @if($manufacturer->address)
                                            <div><i class="fas fa-map-marker-alt text-muted"></i> {{ Str::limit($manufacturer->address, 30) }}</div>
                                        @endif
                                    </td>
                                    <td>
                                        @if($manufacturer->website_url)
                                            <a href="{{ $manufacturer->website_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> Website
                                            </a>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ $manufacturer->active_products_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-light">{{ $manufacturer->sort_order ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input status-toggle" 
                                                   id="status-{{ $manufacturer->id_manufacturer }}"
                                                   data-id="{{ $manufacturer->id_manufacturer }}"
                                                   {{ $manufacturer->is_active ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="status-{{ $manufacturer->id_manufacturer }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        @if($manufacturer->is_featured)
                                            <i class="fas fa-star text-warning" title="Nổi bật"></i>
                                        @else
                                            <i class="far fa-star text-muted" title="Không nổi bật"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.manufacturers.show', $manufacturer->id_manufacturer) }}" 
                                               class="btn btn-info btn-sm" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.manufacturers.edit', $manufacturer->id_manufacturer) }}" 
                                               class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" 
                                                    onclick="confirmDelete('{{ route('admin.manufacturers.destroy', $manufacturer->id_manufacturer) }}', 'Bạn có chắc chắn muốn xóa thương hiệu này?')"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-industry fa-3x mb-3"></i>
                                            <p>Không có thương hiệu nào được tìm thấy.</p>
                                            <a href="{{ route('admin.manufacturers.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Thêm thương hiệu đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                @if($manufacturers->hasPages())
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted">
                                Hiển thị {{ $manufacturers->firstItem() }} đến {{ $manufacturers->lastItem() }} 
                                trong tổng số {{ $manufacturers->total() }} thương hiệu
                            </small>
                        </div>
                        <div class="col-md-6">
                            {{ $manufacturers->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Social Media Links Modal -->
    <div class="modal fade" id="socialModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Liên kết mạng xã hội</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="socialContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle status
    $('.status-toggle').on('change', function() {
        const manufacturerId = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/manufacturers/${manufacturerId}/toggle-status`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Có lỗi xảy ra');
                    // Revert toggle
                    $(this).prop('checked', !isActive);
                }
            },
            error: function() {
                toastr.error('Có lỗi xảy ra');
                // Revert toggle
                $(this).prop('checked', !isActive);
            }
        });
    });

    // Show social media links
    function showSocialLinks(manufacturer) {
        let content = '<div class="list-group">';
        
        if (manufacturer.facebook_url) {
            content += `<a href="${manufacturer.facebook_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-facebook text-primary"></i> Facebook
            </a>`;
        }
        
        if (manufacturer.instagram_url) {
            content += `<a href="${manufacturer.instagram_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-instagram text-danger"></i> Instagram
            </a>`;
        }
        
        if (manufacturer.twitter_url) {
            content += `<a href="${manufacturer.twitter_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-twitter text-info"></i> Twitter
            </a>`;
        }
        
        if (manufacturer.youtube_url) {
            content += `<a href="${manufacturer.youtube_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-youtube text-danger"></i> YouTube
            </a>`;
        }
        
        if (!manufacturer.facebook_url && !manufacturer.instagram_url && !manufacturer.twitter_url && !manufacturer.youtube_url) {
            content += '<p class="text-muted">Không có liên kết mạng xã hội nào.</p>';
        }
        
        content += '</div>';
        
        $('#socialContent').html(content);
        $('#socialModal').modal('show');
    }
});
</script>
@endpush
