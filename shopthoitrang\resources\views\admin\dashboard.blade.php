@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('page_title', 'Dashboard')

@section('breadcrumb')
    <li class="breadcrumb-item active">Dashboard</li>
@endsection

@section('content')
    <!-- Statistics Cards -->
    <div class="row">
        <!-- Total Products -->
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_products'] ?? 0 }}</h3>
                    <p>Tổng sản phẩm</p>
                </div>
                <div class="icon">
                    <i class="fas fa-box"></i>
                </div>
                <a href="{{ route('admin.products.index') }}" class="small-box-footer">
                    Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <!-- Total Orders -->
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['total_orders'] ?? 0 }}</h3>
                    <p>Tổng đơn hàng</p>
                </div>
                <div class="icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <a href="{{ route('admin.orders.index') }}" class="small-box-footer">
                    Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <!-- Total Users -->
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['total_users'] ?? 0 }}</h3>
                    <p>Tổng khách hàng</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <a href="{{ route('admin.users.index') }}" class="small-box-footer">
                    Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ number_format($stats['total_revenue'] ?? 0, 0, ',', '.') }}₫</h3>
                    <p>Tổng doanh thu</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <a href="{{ route('admin.reports.sales') }}" class="small-box-footer">
                    Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="info-box">
                <span class="info-box-icon bg-info"><i class="fas fa-clock"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Đơn hàng chờ xử lý</span>
                    <span class="info-box-number">{{ $stats['pending_orders'] ?? 0 }}</span>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="info-box">
                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Đơn hàng hoàn thành</span>
                    <span class="info-box-number">{{ $stats['completed_orders'] ?? 0 }}</span>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="info-box">
                <span class="info-box-icon bg-warning"><i class="fas fa-exclamation-triangle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Sản phẩm sắp hết</span>
                    <span class="info-box-number">{{ $stats['low_stock_products'] ?? 0 }}</span>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="info-box">
                <span class="info-box-icon bg-danger"><i class="fas fa-star"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Đánh giá chờ duyệt</span>
                    <span class="info-box-number">{{ $stats['pending_reviews'] ?? 0 }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sales Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-1"></i>
                        Biểu đồ doanh thu 30 ngày qua
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar mr-1"></i>
                        Thống kê hôm nay
                    </h3>
                </div>
                <div class="card-body">
                    <div class="progress-group">
                        Đơn hàng mới
                        <span class="float-right"><b>{{ $stats['daily_orders'] ?? 0 }}</b>/{{ $stats['total_orders'] ?? 0 }}</span>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-primary" style="width: {{ $stats['total_orders'] > 0 ? ($stats['daily_orders'] / $stats['total_orders']) * 100 : 0 }}%"></div>
                        </div>
                    </div>

                    <div class="progress-group">
                        Doanh thu tháng này
                        <span class="float-right"><b>{{ number_format($stats['monthly_revenue'] ?? 0, 0, ',', '.') }}₫</b></span>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-success" style="width: {{ $stats['total_revenue'] > 0 ? ($stats['monthly_revenue'] / $stats['total_revenue']) * 100 : 0 }}%"></div>
                        </div>
                    </div>

                    <div class="progress-group">
                        Sản phẩm đang hoạt động
                        <span class="float-right"><b>{{ $stats['active_products'] ?? 0 }}</b>/{{ $stats['total_products'] ?? 0 }}</span>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-warning" style="width: {{ $stats['total_products'] > 0 ? ($stats['active_products'] / $stats['total_products']) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-1"></i>
                        Thao tác nhanh
                    </h3>
                </div>
                <div class="card-body">
                    <a href="{{ route('admin.products.create') }}" class="btn btn-primary btn-block">
                        <i class="fas fa-plus"></i> Thêm sản phẩm mới
                    </a>
                    <a href="{{ route('admin.categories.create') }}" class="btn btn-info btn-block">
                        <i class="fas fa-folder-plus"></i> Thêm danh mục
                    </a>
                    <a href="{{ route('admin.orders.index', ['status' => 'pending']) }}" class="btn btn-warning btn-block">
                        <i class="fas fa-clock"></i> Xử lý đơn hàng
                    </a>
                    <a href="{{ route('admin.reports.sales') }}" class="btn btn-success btn-block">
                        <i class="fas fa-chart-bar"></i> Xem báo cáo
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection