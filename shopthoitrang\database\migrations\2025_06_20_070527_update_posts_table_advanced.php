<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('posts', function (Blueprint $table) {
            // Author information
            $table->unsignedBigInteger('author_id')->after('id_post');
            $table->unsignedBigInteger('category_id')->nullable()->after('author_id');

            // SEO fields
            $table->string('slug')->unique()->after('title_post');
            $table->string('meta_title')->nullable()->after('slug');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');

            // Content fields
            $table->text('excerpt')->nullable()->after('meta_keywords');
            $table->string('featured_image')->nullable()->after('content_post');
            $table->json('gallery_images')->nullable()->after('featured_image');

            // Post status and visibility
            $table->enum('status', ['draft', 'published', 'scheduled', 'archived'])->default('draft')->after('gallery_images');
            $table->boolean('is_featured')->default(false)->after('status');
            $table->boolean('allow_comments')->default(true)->after('is_featured');
            $table->timestamp('published_at')->nullable()->after('allow_comments');

            // Post statistics
            $table->integer('view_count')->default(0)->after('published_at');
            $table->integer('comment_count')->default(0)->after('view_count');
            $table->integer('like_count')->default(0)->after('comment_count');

            // Tags and categories
            $table->json('tags')->nullable()->after('like_count');

            // Reading time and content analysis
            $table->integer('reading_time')->nullable()->after('tags'); // in minutes
            $table->integer('word_count')->nullable()->after('reading_time');

            // Social sharing
            $table->integer('share_count')->default(0)->after('word_count');

            // Foreign keys
            $table->foreign('author_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('category_id')->references('id_category')->on('categories')->onDelete('set null');

            // Indexes
            $table->index(['status', 'published_at']);
            $table->index(['author_id', 'status']);
            $table->index(['category_id', 'status']);
            $table->index(['is_featured', 'status']);
            $table->index('view_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropForeign(['author_id']);
            $table->dropForeign(['category_id']);
            $table->dropColumn([
                'author_id', 'category_id', 'slug', 'meta_title', 'meta_description',
                'meta_keywords', 'excerpt', 'featured_image', 'gallery_images', 'status',
                'is_featured', 'allow_comments', 'published_at', 'view_count', 'comment_count',
                'like_count', 'tags', 'reading_time', 'word_count', 'share_count'
            ]);
        });
    }
};
