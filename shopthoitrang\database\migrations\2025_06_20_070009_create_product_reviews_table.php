<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('order_id')->nullable(); // For verified purchase

            // Review content
            $table->string('title');
            $table->text('content');
            $table->tinyInteger('rating')->unsigned(); // 1-5 stars

            // Review status
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->boolean('is_verified_purchase')->default(false);
            $table->boolean('is_featured')->default(false);

            // Helpful votes
            $table->integer('helpful_votes')->default(0);
            $table->integer('total_votes')->default(0);

            // Review metadata
            $table->json('pros')->nullable(); // Array of pros
            $table->json('cons')->nullable(); // Array of cons
            $table->json('images')->nullable(); // Review images

            // Moderation
            $table->text('admin_notes')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();

            $table->timestamps();

            // Foreign keys
            $table->foreign('product_id')->references('id_product')->on('products')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('order_id')->references('id_order')->on('order')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['product_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index(['rating', 'status']);
            $table->index(['is_featured', 'status']);

            // Unique constraint to prevent multiple reviews per user per product
            $table->unique(['product_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_reviews');
    }
};
