<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\SeoMetadata;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of categories with hierarchy
     */
    public function index(Request $request)
    {
        $query = Category::with(['parent', 'children'])
                        ->withCount('activeProducts');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name_category', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by parent
        if ($request->filled('parent_id')) {
            if ($request->parent_id === 'root') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        }

        $categories = $query->orderBy('sort_order')
                           ->orderBy('name_category')
                           ->paginate(15);

        $parentCategories = Category::whereNull('parent_id')
                                  ->where('is_active', true)
                                  ->orderBy('name_category')
                                  ->get();

        return view('admin.categories.index', compact('categories', 'parentCategories'));
    }

    /**
     * Show the form for creating a new category
     */
    public function create()
    {
        $parentCategories = Category::whereNull('parent_id')
                                  ->where('is_active', true)
                                  ->orderBy('name_category')
                                  ->get();

        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created category
     */
    public function store(Request $request)
    {
        $request->validate([
            'name_category' => 'required|string|max:100',
            'parent_id' => 'nullable|exists:categories,id_category',
            'slug' => 'required|string|max:100|unique:categories,slug',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'show_in_menu' => 'boolean',
            'featured' => 'boolean',
            'template' => 'nullable|string|max:100'
        ], [
            'name_category.required' => 'Tên danh mục là bắt buộc',
            'name_category.max' => 'Tên danh mục không được vượt quá 100 ký tự',
            'slug.required' => 'Slug là bắt buộc',
            'slug.unique' => 'Slug đã tồn tại',
            'parent_id.exists' => 'Danh mục cha không tồn tại',
            'image.image' => 'File phải là hình ảnh',
            'image.max' => 'Hình ảnh không được vượt quá 2MB',
            'banner_image.max' => 'Banner không được vượt quá 5MB'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->only([
                'name_category', 'parent_id', 'slug', 'meta_title',
                'meta_description', 'meta_keywords', 'description',
                'icon', 'sort_order', 'template'
            ]);

            $data['is_active'] = $request->boolean('is_active', true);
            $data['show_in_menu'] = $request->boolean('show_in_menu', true);
            $data['featured'] = $request->boolean('featured', false);
            $data['sort_order'] = $request->input('sort_order', 0);

            // Handle image upload
            if ($request->hasFile('image')) {
                $data['image'] = $this->uploadImage($request->file('image'), 'categories');
            }

            // Handle banner image upload
            if ($request->hasFile('banner_image')) {
                $data['banner_image'] = $this->uploadImage($request->file('banner_image'), 'categories/banners');
            }

            $category = Category::create($data);

            DB::commit();

            return redirect()->route('admin.categories.index')
                           ->with('success', 'Danh mục đã được tạo thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error creating category: ' . $e->getMessage());
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Show the form for editing a category
     */
    public function show(Category $category)
    {
        $category->load(['parent', 'children', 'activeProducts']);
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing a category
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::whereNull('parent_id')
                                  ->where('is_active', true)
                                  ->where('id_category', '!=', $category->id_category)
                                  ->orderBy('name_category')
                                  ->get();

        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified category
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name_category' => 'required|string|max:100',
            'parent_id' => [
                'nullable',
                'exists:categories,id_category',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value == $category->id_category) {
                        $fail('Danh mục không thể là cha của chính nó.');
                    }
                    // Check for circular reference
                    if ($value && $this->wouldCreateCircularReference($category->id_category, $value)) {
                        $fail('Không thể tạo tham chiếu vòng trong cây danh mục.');
                    }
                }
            ],
            'slug' => [
                'required',
                'string',
                'max:100',
                Rule::unique('categories', 'slug')->ignore($category->id_category, 'id_category')
            ],
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'show_in_menu' => 'boolean',
            'featured' => 'boolean',
            'template' => 'nullable|string|max:100'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->only([
                'name_category', 'parent_id', 'slug', 'meta_title',
                'meta_description', 'meta_keywords', 'description',
                'icon', 'sort_order', 'template'
            ]);

            $data['is_active'] = $request->boolean('is_active', true);
            $data['show_in_menu'] = $request->boolean('show_in_menu', true);
            $data['featured'] = $request->boolean('featured', false);

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($category->image) {
                    $this->deleteImage($category->image);
                }
                $data['image'] = $this->uploadImage($request->file('image'), 'categories');
            }

            // Handle banner image upload
            if ($request->hasFile('banner_image')) {
                // Delete old banner
                if ($category->banner_image) {
                    $this->deleteImage($category->banner_image);
                }
                $data['banner_image'] = $this->uploadImage($request->file('banner_image'), 'categories/banners');
            }

            $category->update($data);

            DB::commit();

            return redirect()->route('admin.categories.index')
                           ->with('success', 'Danh mục đã được cập nhật thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error updating category: ' . $e->getMessage());
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified category
     */
    public function destroy(Category $category)
    {
        try {
            // Check if category has children
            if ($category->children()->count() > 0) {
                return redirect()->back()
                               ->with('error', 'Không thể xóa danh mục có danh mục con. Vui lòng xóa các danh mục con trước.');
            }

            // Check if category has products
            if ($category->activeProducts()->count() > 0) {
                return redirect()->back()
                               ->with('error', 'Không thể xóa danh mục có sản phẩm. Vui lòng di chuyển sản phẩm sang danh mục khác trước.');
            }

            DB::beginTransaction();

            // Delete images
            if ($category->image) {
                $this->deleteImage($category->image);
            }
            if ($category->banner_image) {
                $this->deleteImage($category->banner_image);
            }

            $category->delete();

            DB::commit();

            return redirect()->route('admin.categories.index')
                           ->with('success', 'Danh mục đã được xóa thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error deleting category: ' . $e->getMessage());
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Upload image and return path
     */
    private function uploadImage($file, $directory = 'categories')
    {
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs("public/{$directory}", $filename);
        return str_replace('public/', '', $path);
    }

    /**
     * Delete image file
     */
    private function deleteImage($imagePath)
    {
        if (Storage::exists("public/{$imagePath}")) {
            Storage::delete("public/{$imagePath}");
        }
    }

    /**
     * Check if setting parent would create circular reference
     */
    private function wouldCreateCircularReference($categoryId, $parentId)
    {
        $parent = Category::find($parentId);
        while ($parent) {
            if ($parent->id_category == $categoryId) {
                return true;
            }
            $parent = $parent->parent;
        }
        return false;
    }

    // Legacy methods for backward compatibility
    public function indexCategory(Request $request)
    {
        return $this->index($request);
    }

    public function indexcreateCategory()
    {
        return $this->create();
    }

    public function createCategory(Request $request)
    {
        return $this->store($request);
    }

    public function indexupdateCategory($id)
    {
        $category = Category::where('id_category', $id)->firstOrFail();
        return $this->edit($category);
    }

    public function updateCategory(Request $request)
    {
        $category = Category::where('id_category', $request->id)->firstOrFail();
        return $this->update($request, $category);
    }

    public function deleteCategory($id)
    {
        $category = Category::where('id_category', $id)->firstOrFail();
        return $this->destroy($category);
    }

    /**
     * Generate slug from category name
     */
    public function generateSlug(Request $request)
    {
        $name = $request->input('name');
        $slug = Str::slug($name);

        // Check if slug exists and make it unique
        $originalSlug = $slug;
        $counter = 1;

        while (Category::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return response()->json(['slug' => $slug]);
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(Category $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return response()->json([
            'success' => true,
            'message' => "Danh mục đã được {$status}",
            'status' => $category->is_active
        ]);
    }

    /**
     * Get category hierarchy for select dropdown
     */
    public function getHierarchy()
    {
        $categories = Category::with('children')
                            ->whereNull('parent_id')
                            ->where('is_active', true)
                            ->orderBy('sort_order')
                            ->orderBy('name_category')
                            ->get();

        return response()->json($categories);
    }
}
