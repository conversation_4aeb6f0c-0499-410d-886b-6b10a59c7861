<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('categories', function (Blueprint $table) {
            // Hierarchical structure
            $table->unsignedBigInteger('parent_id')->nullable()->after('id_category');
            $table->integer('sort_order')->default(0)->after('parent_id');
            $table->boolean('is_active')->default(true)->after('sort_order');

            // SEO fields
            $table->string('slug')->unique()->after('name_category');
            $table->string('meta_title')->nullable()->after('slug');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');

            // Content fields
            $table->text('description')->nullable()->after('meta_keywords');
            $table->string('image')->nullable()->after('description');
            $table->string('banner_image')->nullable()->after('image');
            $table->string('icon')->nullable()->after('banner_image');

            // Display settings
            $table->boolean('show_in_menu')->default(true)->after('icon');
            $table->boolean('featured')->default(false)->after('show_in_menu');
            $table->string('template')->nullable()->after('featured');

            // Foreign key constraint
            $table->foreign('parent_id')->references('id_category')->on('categories')->onDelete('set null');

            // Indexes
            $table->index(['parent_id', 'is_active']);
            $table->index(['featured', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn([
                'parent_id', 'sort_order', 'is_active', 'slug', 'meta_title',
                'meta_description', 'meta_keywords', 'description', 'image',
                'banner_image', 'icon', 'show_in_menu', 'featured', 'template'
            ]);
        });
    }
};
