<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;
use App\Models\Manufacturer;
use App\Models\SeoMetadata;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ManufacturerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of manufacturers
     */
    public function index(Request $request)
    {
        $query = Manufacturer::withCount('activeProducts');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name_manufacturer', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by featured
        if ($request->filled('featured')) {
            $query->where('is_featured', $request->featured === 'yes');
        }

        $manufacturers = $query->orderBy('sort_order')
                             ->orderBy('name_manufacturer')
                             ->paginate(15);

        return view('admin.manufacturers.index', compact('manufacturers'));
    }

    /**
     * Show the form for creating a new manufacturer
     */
    public function create()
    {
        return view('admin.manufacturers.create');
    }

    /**
     * Store a newly created manufacturer
     */
    public function store(Request $request)
    {
        $request->validate([
            'name_manufacturer' => 'required|string|max:100',
            'slug' => 'required|string|max:100|unique:manufacturers,slug',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'website_url' => 'nullable|url',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'facebook_url' => 'nullable|url',
            'instagram_url' => 'nullable|url',
            'twitter_url' => 'nullable|url',
            'youtube_url' => 'nullable|url',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean'
        ], [
            'name_manufacturer.required' => 'Tên thương hiệu là bắt buộc',
            'slug.required' => 'Slug là bắt buộc',
            'slug.unique' => 'Slug đã tồn tại',
            'logo.image' => 'File phải là hình ảnh',
            'logo.max' => 'Logo không được vượt quá 2MB',
            'banner_image.max' => 'Banner không được vượt quá 5MB',
            'website_url.url' => 'Website URL không hợp lệ',
            'email.email' => 'Email không hợp lệ'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->only([
                'name_manufacturer', 'slug', 'meta_title', 'meta_description',
                'meta_keywords', 'description', 'website_url', 'email', 'phone',
                'address', 'city', 'country', 'facebook_url', 'instagram_url',
                'twitter_url', 'youtube_url', 'sort_order'
            ]);

            $data['is_active'] = $request->boolean('is_active', true);
            $data['is_featured'] = $request->boolean('is_featured', false);
            $data['sort_order'] = $request->input('sort_order', 0);
            $data['product_count'] = 0;

            // Handle logo upload
            if ($request->hasFile('logo')) {
                $data['logo'] = $this->uploadImage($request->file('logo'), 'manufacturers/logos');
            }

            // Handle banner upload
            if ($request->hasFile('banner_image')) {
                $data['banner_image'] = $this->uploadImage($request->file('banner_image'), 'manufacturers/banners');
            }

            $manufacturer = Manufacturer::create($data);

            DB::commit();

            return redirect()->route('admin.manufacturers.index')
                           ->with('success', 'Thương hiệu đã được tạo thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified manufacturer
     */
    public function show(Manufacturer $manufacturer)
    {
        $manufacturer->load(['activeProducts']);
        return view('admin.manufacturers.show', compact('manufacturer'));
    }

    /**
     * Show the form for editing the specified manufacturer
     */
    public function edit(Manufacturer $manufacturer)
    {
        return view('admin.manufacturers.edit', compact('manufacturer'));
    }

    /**
     * Update the specified manufacturer
     */
    public function update(Request $request, Manufacturer $manufacturer)
    {
        $request->validate([
            'name_manufacturer' => 'required|string|max:100',
            'slug' => [
                'required',
                'string',
                'max:100',
                Rule::unique('manufacturers', 'slug')->ignore($manufacturer->id_manufacturer, 'id_manufacturer')
            ],
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'website_url' => 'nullable|url',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'facebook_url' => 'nullable|url',
            'instagram_url' => 'nullable|url',
            'twitter_url' => 'nullable|url',
            'youtube_url' => 'nullable|url',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean'
        ]);

        try {
            DB::beginTransaction();

            $data = $request->only([
                'name_manufacturer', 'slug', 'meta_title', 'meta_description',
                'meta_keywords', 'description', 'website_url', 'email', 'phone',
                'address', 'city', 'country', 'facebook_url', 'instagram_url',
                'twitter_url', 'youtube_url', 'sort_order'
            ]);

            $data['is_active'] = $request->boolean('is_active', true);
            $data['is_featured'] = $request->boolean('is_featured', false);

            // Handle logo upload
            if ($request->hasFile('logo')) {
                // Delete old logo
                if ($manufacturer->logo) {
                    $this->deleteImage($manufacturer->logo);
                }
                $data['logo'] = $this->uploadImage($request->file('logo'), 'manufacturers/logos');
            }

            // Handle banner upload
            if ($request->hasFile('banner_image')) {
                // Delete old banner
                if ($manufacturer->banner_image) {
                    $this->deleteImage($manufacturer->banner_image);
                }
                $data['banner_image'] = $this->uploadImage($request->file('banner_image'), 'manufacturers/banners');
            }

            $manufacturer->update($data);

            DB::commit();

            return redirect()->route('admin.manufacturers.index')
                           ->with('success', 'Thương hiệu đã được cập nhật thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified manufacturer
     */
    public function destroy(Manufacturer $manufacturer)
    {
        try {
            // Check if manufacturer has products
            if ($manufacturer->activeProducts()->count() > 0) {
                return redirect()->back()
                               ->with('error', 'Không thể xóa thương hiệu có sản phẩm. Vui lòng di chuyển sản phẩm sang thương hiệu khác trước.');
            }

            DB::beginTransaction();

            // Delete images
            if ($manufacturer->logo) {
                $this->deleteImage($manufacturer->logo);
            }
            if ($manufacturer->banner_image) {
                $this->deleteImage($manufacturer->banner_image);
            }

            $manufacturer->delete();

            DB::commit();

            return redirect()->route('admin.manufacturers.index')
                           ->with('success', 'Thương hiệu đã được xóa thành công!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                           ->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Generate slug from manufacturer name
     */
    public function generateSlug(Request $request)
    {
        $name = $request->input('name');
        $slug = Str::slug($name);

        // Check if slug exists and make it unique
        $originalSlug = $slug;
        $counter = 1;

        while (Manufacturer::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return response()->json(['slug' => $slug]);
    }

    /**
     * Toggle manufacturer status
     */
    public function toggleStatus(Manufacturer $manufacturer)
    {
        $manufacturer->update(['is_active' => !$manufacturer->is_active]);

        $status = $manufacturer->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return response()->json([
            'success' => true,
            'message' => "Thương hiệu đã được {$status}",
            'status' => $manufacturer->is_active
        ]);
    }

    /**
     * Upload image and return path
     */
    private function uploadImage($file, $directory = 'manufacturers')
    {
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs("public/{$directory}", $filename);
        return str_replace('public/', '', $path);
    }

    /**
     * Delete image file
     */
    private function deleteImage($imagePath)
    {
        if (Storage::exists("public/{$imagePath}")) {
            Storage::delete("public/{$imagePath}");
        }
    }

    // Legacy methods for backward compatibility
    public function indexManufacturer(){
        $manufacturers = Manufacturer::getManufacturersWithPagination(2);
        return view('admin.manufacturer.listManufacturer', ['manufacturers' => $manufacturers]);
    }    

    public function indexAddManufacturer(){
        return view('admin.manufacturer.addmanufacturer');
    }
    
    public function addManufacturer(Request $request){
        try {
            $request->validate([
                'name_manufacturer' => [
                    'required',
                    'string',
                    'max:100',
                    'regex:/^[a-zA-Z0-9À-ỹ\s\.,\-()]+$/u'
                ],
                'image_manufacturer' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ], [
                'name_manufacturer.required' => 'Vui lòng nhập tên hãng sản xuất',
                'name_manufacturer.max' => 'Tên hãng không quá 100 ký tự',
                'name_manufacturer.regex' => 'Tên hãng chỉ được chứa chữ, số và một số ký tự hợp lệ',
                'image_manufacturer.required' => 'Vui lòng chọn ảnh hãng sản xuất',
                'image_manufacturer.image' => 'File phải là ảnh',
                'image_manufacturer.mimes' => 'Chỉ chấp nhận ảnh jpeg, png, jpg, gif',
                'image_manufacturer.max' => 'Ảnh không quá 2MB',
            ]);

            $data = $request->all();

            if($request->hasFile('image_manufacturer')) {
                $file = $request->file('image_manufacturer');
                $ex = $file->getClientOriginalExtension();
                $filename = time().'.'.$ex;
                $file->move('uploads/manufacturerimage/',$filename);
                $data['image_manufacturer'] = $filename;
            }

            Manufacturer::create([
                'name_manufacturer' => $data['name_manufacturer'],
                'image_manufacturer' => $data['image_manufacturer']
            ]);

            return redirect()->route('manufacturer.listmanufacturer')->with('success', 'Thêm hãng sản xuất thành công');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Có lỗi xảy ra khi thêm hãng sản xuất: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function deleteManufacturer(Request $request){
        try {
            $manufacturer_id = $request->get('id');
            $manufacturer = Manufacturer::findOrFail($manufacturer_id);
            
            // Xóa ảnh nếu có
            if ($manufacturer->image_manufacturer) {
                $imagePath = 'uploads/manufacturerimage/' . $manufacturer->image_manufacturer;
                if (File::exists($imagePath)) {
                    File::delete($imagePath);
                }
            }

            $manufacturer->delete();
            return redirect()->route('manufacturer.listmanufacturer')->with('success', 'Xóa hãng sản xuất thành công');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi xóa hãng sản xuất: ' . $e->getMessage());
        }
    }
    
    public function indexUpdateManufacturer(Request $request){
        $manufacturer_id = $request->get('id');
        $manufacturer = Manufacturer::findManufacturerById($manufacturer_id);
        return view('admin.manufacturer.updatemanufacturer', ['manufacturer' => $manufacturer]);
    }

    public function updateManufacturer(Request $request)
    {
        $input = $request->all();
        
        $request->validate([
            'name' => 'required',
            'description' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Kiểm tra manufacturer có tồn tại không
        $manufacturer = Manufacturer::find($input['id']);
        
        if (!$manufacturer) {
            // Log lỗi chi tiết
            Log::warning('Attempt to update non-existent manufacturer', [
                'manufacturer_id' => $input['id'],
                'attempted_by' => session('id_user'),
                'timestamp' => now(),
                'ip_address' => request()->ip()
            ]);
            
            // Lưu thông báo lỗi vào session để hiển thị sau reload
            return redirect('listmanufacturer')->withErrors([
                'error' => 'Hãng sản xuất ID#' . $input['id'] . ' không tồn tại hoặc đã bị xóa bởi người khác! Danh sách đã được cập nhật.'
            ])->with('reload_needed', true);
        }

        // Kiểm tra manufacturer có products không (nếu có thể ảnh hưởng)
        if ($manufacturer->products && $manufacturer->products->count() > 0) {
            Log::info('Updating manufacturer with existing products', [
                'manufacturer_id' => $input['id'],
                'products_count' => $manufacturer->products->count(),
                'manufacturer_name' => $manufacturer->name
            ]);
        }

        // Cập nhật thông tin
        $manufacturer->name = $input['name'];
        $manufacturer->description = $input['description'];
        
        // Xử lý upload image nếu có
        if ($request->hasFile('image')) {
            // Xóa ảnh cũ nếu cần
            if ($manufacturer->image && file_exists(public_path('images/manufacturers/' . $manufacturer->image))) {
                unlink(public_path('images/manufacturers/' . $manufacturer->image));
            }
            
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('images/manufacturers'), $imageName);
            $manufacturer->image = $imageName;
        }
        
        $manufacturer->save();
        
        // Log thành công
        Log::info('Manufacturer updated successfully', [
            'manufacturer_id' => $manufacturer->id,
            'manufacturer_name' => $manufacturer->name,
            'updated_by' => session('id_user')
        ]);
        
        return redirect('listmanufacturer')->with('success', 'Cập nhật hãng sản xuất thành công!');
    }
}