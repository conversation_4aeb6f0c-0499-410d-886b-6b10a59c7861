<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id('id_category');

            // Hierarchical structure
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);

            // Basic info
            $table->string('name_category', 100);

            // SEO fields
            $table->string('slug')->unique();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();

            // Content fields
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->string('banner_image')->nullable();
            $table->string('icon')->nullable();

            // Display settings
            $table->boolean('show_in_menu')->default(true);
            $table->boolean('featured')->default(false);
            $table->string('template')->nullable();

            $table->timestamps();

            // Foreign key constraint
            $table->foreign('parent_id')->references('id_category')->on('categories')->onDelete('set null');

            // Indexes
            $table->index(['parent_id', 'is_active']);
            $table->index(['featured', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('categories');
    }
};
