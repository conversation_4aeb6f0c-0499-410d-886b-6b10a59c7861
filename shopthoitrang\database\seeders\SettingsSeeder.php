<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings = [
            // General Settings
            ['key' => 'site_name', 'value' => 'Shop Thời Trang Việt Nam', 'type' => 'string', 'group' => 'general', 'label' => 'Tên Website', 'description' => 'Tên chính của website', 'is_public' => true, 'sort_order' => 1],
            ['key' => 'site_description', 'value' => 'Cửa hàng thời trang trực tuyến hàng đầu Việt Nam với các sản phẩm chất lượng cao', 'type' => 'text', 'group' => 'general', 'label' => 'Mô tả Website', 'description' => '<PERSON><PERSON> tả ngắn về website', 'is_public' => true, 'sort_order' => 2],
            ['key' => 'site_keywords', 'value' => 'thời trang, quần <PERSON>, gi<PERSON><PERSON>, phụ kiện, mua sắm online', 'type' => 'text', 'group' => 'general', 'label' => 'Từ khóa Website', 'description' => 'Từ khóa chính của website', 'is_public' => true, 'sort_order' => 3],
            ['key' => 'site_logo', 'value' => '/images/logo.png', 'type' => 'file', 'group' => 'general', 'label' => 'Logo Website', 'description' => 'Logo chính của website', 'is_public' => true, 'sort_order' => 4],
            ['key' => 'site_favicon', 'value' => '/images/favicon.ico', 'type' => 'file', 'group' => 'general', 'label' => 'Favicon', 'description' => 'Icon nhỏ hiển thị trên tab trình duyệt', 'is_public' => true, 'sort_order' => 5],

            // Contact Information
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'contact', 'label' => 'Email Liên Hệ', 'description' => 'Email chính để liên hệ', 'is_public' => true, 'sort_order' => 1],
            ['key' => 'contact_phone', 'value' => '0123456789', 'type' => 'string', 'group' => 'contact', 'label' => 'Số Điện Thoại', 'description' => 'Số điện thoại liên hệ', 'is_public' => true, 'sort_order' => 2],
            ['key' => 'contact_address', 'value' => '123 Đường ABC, Quận 1, TP.HCM', 'type' => 'text', 'group' => 'contact', 'label' => 'Địa Chỉ', 'description' => 'Địa chỉ cửa hàng', 'is_public' => true, 'sort_order' => 3],

            // SEO Settings
            ['key' => 'seo_title_separator', 'value' => ' | ', 'type' => 'string', 'group' => 'seo', 'label' => 'Ký Tự Phân Cách Title', 'description' => 'Ký tự phân cách trong title tag', 'is_public' => false, 'sort_order' => 1],
            ['key' => 'seo_meta_description', 'value' => 'Khám phá bộ sưu tập thời trang đa dạng tại Shop Thời Trang Việt Nam. Chất lượng cao, giá cả hợp lý, giao hàng nhanh chóng.', 'type' => 'text', 'group' => 'seo', 'label' => 'Meta Description Mặc Định', 'description' => 'Meta description cho trang chủ', 'is_public' => true, 'sort_order' => 2],
            ['key' => 'seo_og_image', 'value' => '/images/og-image.jpg', 'type' => 'file', 'group' => 'seo', 'label' => 'Hình Ảnh Open Graph', 'description' => 'Hình ảnh hiển thị khi chia sẻ trên mạng xã hội', 'is_public' => true, 'sort_order' => 3],

            // E-commerce Settings
            ['key' => 'currency', 'value' => 'VND', 'type' => 'string', 'group' => 'ecommerce', 'label' => 'Đơn Vị Tiền Tệ', 'description' => 'Đơn vị tiền tệ chính', 'is_public' => true, 'sort_order' => 1],
            ['key' => 'currency_symbol', 'value' => '₫', 'type' => 'string', 'group' => 'ecommerce', 'label' => 'Ký Hiệu Tiền Tệ', 'description' => 'Ký hiệu hiển thị cho tiền tệ', 'is_public' => true, 'sort_order' => 2],
            ['key' => 'tax_rate', 'value' => '10', 'type' => 'integer', 'group' => 'ecommerce', 'label' => 'Thuế VAT (%)', 'description' => 'Tỷ lệ thuế VAT mặc định', 'is_public' => false, 'sort_order' => 3],
            ['key' => 'free_shipping_threshold', 'value' => '500000', 'type' => 'integer', 'group' => 'ecommerce', 'label' => 'Ngưỡng Miễn Phí Ship', 'description' => 'Giá trị đơn hàng tối thiểu để được miễn phí vận chuyển', 'is_public' => true, 'sort_order' => 4],

            // Social Media
            ['key' => 'facebook_url', 'value' => 'https://facebook.com/shopthoitrang', 'type' => 'string', 'group' => 'social', 'label' => 'Facebook URL', 'description' => 'Đường dẫn trang Facebook', 'is_public' => true, 'sort_order' => 1],
            ['key' => 'instagram_url', 'value' => 'https://instagram.com/shopthoitrang', 'type' => 'string', 'group' => 'social', 'label' => 'Instagram URL', 'description' => 'Đường dẫn trang Instagram', 'is_public' => true, 'sort_order' => 2],
            ['key' => 'youtube_url', 'value' => 'https://youtube.com/shopthoitrang', 'type' => 'string', 'group' => 'social', 'label' => 'YouTube URL', 'description' => 'Đường dẫn kênh YouTube', 'is_public' => true, 'sort_order' => 3],
        ];

        foreach ($settings as $setting) {
            DB::table('settings')->updateOrInsert(
                ['key' => $setting['key']],
                array_merge($setting, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
