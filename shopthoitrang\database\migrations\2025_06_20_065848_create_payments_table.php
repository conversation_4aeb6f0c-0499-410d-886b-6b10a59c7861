<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->string('payment_method'); // credit_card, paypal, bank_transfer, cod, momo, zalopay
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])
                  ->default('pending');

            // Payment amounts
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('VND');
            $table->decimal('fee', 10, 2)->default(0);

            // Payment gateway information
            $table->string('gateway')->nullable(); // stripe, paypal, vnpay, momo
            $table->string('transaction_id')->nullable();
            $table->string('gateway_transaction_id')->nullable();
            $table->json('gateway_response')->nullable();

            // Payment details
            $table->timestamp('processed_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->text('notes')->nullable();

            // Refund information
            $table->decimal('refunded_amount', 10, 2)->default(0);
            $table->timestamp('refunded_at')->nullable();
            $table->text('refund_reason')->nullable();

            $table->timestamps();

            // Foreign key
            $table->foreign('order_id')->references('id_order')->on('order')->onDelete('cascade');

            // Indexes
            $table->index(['order_id', 'status']);
            $table->index('transaction_id');
            $table->index('gateway_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payments');
    }
};
