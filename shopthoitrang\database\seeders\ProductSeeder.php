<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $products = [
            [
                'id_category' => 1,
                'id_manufacturer' => 1,
                'name_product' => 'Á<PERSON> Mi Nam Trắng Công Sở',
                'slug' => 'ao-so-mi-nam-trang-cong-so',
                'meta_title' => 'Áo <PERSON>ơ Mi Nam Trắng Công Sở - Chấ<PERSON>, Gi<PERSON>ốt',
                'meta_description' => 'Áo sơ mi nam trắng công sở chất liệu cotton cao cấp, form slimfit, phù hợp đi làm và dự tiệc. Giá chỉ từ 299.000đ.',
                'meta_keywords' => 'áo sơ mi nam, áo sơ mi trắng, áo sơ mi công sở',
                'quantity_product' => 100,
                'price_product' => 299000,
                'compare_price' => 399000,
                'cost_price' => 200000,
                'image_address_product' => 'ao-so-mi-nam-trang.jpg',
                'describe_product' => 'Áo sơ mi nam trắng công sở chất liệu cotton cao cấp, form slimfit hiện đại. Thiết kế tinh tế với đường may chắc chắn, phù hợp cho môi trường công sở và các dịp trang trọng.',
                'short_description' => 'Áo sơ mi nam trắng công sở chất liệu cotton cao cấp, form slimfit.',
                'specifications' => 'Chất liệu: Cotton 100%; Xuất xứ: Việt Nam; Form: Slimfit; Màu sắc: Trắng',
                'status' => 'active',
                'is_featured' => true,
                'is_digital' => false,
                'track_inventory' => true,
                'min_stock_level' => 10,
                'allow_backorder' => false,
                'weight' => 0.3,
                'requires_shipping' => true,
                'average_rating' => 4.5,
                'review_count' => 25,
                'tags' => json_encode(['áo sơ mi', 'công sở', 'nam', 'trắng']),
                'has_variants' => true,
                'variant_options' => json_encode(['size' => ['S', 'M', 'L', 'XL'], 'color' => ['Trắng']]),
                'view_count' => 150
            ],
            [
                'id_category' => 2,
                'id_manufacturer' => 2,
                'name_product' => 'Váy Đầm Nữ Dự Tiệc Sang Trọng',
                'slug' => 'vay-dam-nu-du-tiec-sang-trong',
                'meta_title' => 'Váy Đầm Nữ Dự Tiệc Sang Trọng - Thiết Kế Cao Cấp',
                'meta_description' => 'Váy đầm nữ dự tiệc sang trọng với thiết kế tinh tế, chất liệu cao cấp. Hoàn hảo cho các buổi tiệc và sự kiện quan trọng.',
                'meta_keywords' => 'váy đầm nữ, đầm dự tiệc, váy sang trọng',
                'quantity_product' => 50,
                'price_product' => 899000,
                'compare_price' => 1200000,
                'cost_price' => 600000,
                'image_address_product' => 'vay-dam-du-tiec.jpg',
                'describe_product' => 'Váy đầm nữ dự tiệc với thiết kế sang trọng, chất liệu voan cao cấp phối ren tinh tế. Dáng váy ôm body tôn lên vóc dáng quyến rũ, phù hợp cho các buổi tiệc tối và sự kiện quan trọng.',
                'short_description' => 'Váy đầm nữ dự tiệc sang trọng, chất liệu voan cao cấp.',
                'specifications' => 'Chất liệu: Voan + Ren; Xuất xứ: Việt Nam; Dáng: Ôm body; Màu sắc: Đen, Đỏ',
                'status' => 'active',
                'is_featured' => true,
                'is_digital' => false,
                'track_inventory' => true,
                'min_stock_level' => 5,
                'allow_backorder' => false,
                'weight' => 0.4,
                'requires_shipping' => true,
                'average_rating' => 4.8,
                'review_count' => 15,
                'tags' => json_encode(['váy đầm', 'dự tiệc', 'nữ', 'sang trọng']),
                'has_variants' => true,
                'variant_options' => json_encode(['size' => ['S', 'M', 'L'], 'color' => ['Đen', 'Đỏ']]),
                'view_count' => 200
            ]
        ];

        foreach ($products as $product) {
            DB::table('products')->insert(array_merge($product, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}