<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->string('sku')->unique();
            $table->string('variant_name'); // e.g., "Red - Large", "Blue - Medium"

            // Variant attributes
            $table->json('attributes'); // {"color": "red", "size": "large"}

            // Pricing and inventory
            $table->decimal('price', 10, 2);
            $table->decimal('compare_price', 10, 2)->nullable();
            $table->integer('quantity')->default(0);
            $table->integer('min_stock_level')->default(0);

            // Physical properties
            $table->decimal('weight', 8, 2)->nullable();
            $table->string('barcode')->nullable();

            // Status
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);

            // Images
            $table->string('image')->nullable();

            $table->timestamps();

            // Foreign key
            $table->foreign('product_id')->references('id_product')->on('products')->onDelete('cascade');

            // Indexes
            $table->index(['product_id', 'is_active']);
            $table->index('sku');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_variants');
    }
};
