<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seo_metadata', function (Blueprint $table) {
            $table->id();
            $table->string('seoable_type'); // Model type (Product, Category, etc.)
            $table->unsignedBigInteger('seoable_id'); // Model ID

            // Basic SEO fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();

            // Open Graph tags
            $table->string('og_title')->nullable();
            $table->text('og_description')->nullable();
            $table->string('og_image')->nullable();
            $table->string('og_type')->default('website');

            // Twitter Card tags
            $table->string('twitter_card')->default('summary');
            $table->string('twitter_title')->nullable();
            $table->text('twitter_description')->nullable();
            $table->string('twitter_image')->nullable();

            // Schema.org structured data
            $table->json('schema_markup')->nullable();

            // Additional SEO settings
            $table->boolean('noindex')->default(false);
            $table->boolean('nofollow')->default(false);
            $table->string('robots')->nullable(); // Custom robots meta

            // Focus keyword and SEO score
            $table->string('focus_keyword')->nullable();
            $table->integer('seo_score')->nullable(); // 0-100

            $table->timestamps();

            // Polymorphic index
            $table->index(['seoable_type', 'seoable_id']);
            $table->unique(['seoable_type', 'seoable_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('seo_metadata');
    }
};
