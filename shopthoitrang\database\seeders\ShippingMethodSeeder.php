<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ShippingMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $shippingMethods = [
            [
                'name' => 'Giao Hàng Tiê<PERSON>',
                'code' => 'standard',
                'description' => 'Giao hàng trong 3-5 ngày làm việc',
                'base_cost' => 30000,
                'cost_per_kg' => 5000,
                'free_shipping_threshold' => 500000,
                'min_delivery_days' => 3,
                'max_delivery_days' => 5,
                'available_countries' => json_encode(['VN']),
                'is_active' => true,
                'requires_signature' => false,
                'insurance_available' => false,
                'max_weight' => 30.00,
                'sort_order' => 1
            ],
            [
                'name' => 'Giao Hàng Nhanh',
                'code' => 'express',
                'description' => 'Giao hàng trong 1-2 ngày làm việc',
                'base_cost' => 50000,
                'cost_per_kg' => 8000,
                'free_shipping_threshold' => 1000000,
                'min_delivery_days' => 1,
                'max_delivery_days' => 2,
                'available_countries' => json_encode(['VN']),
                'is_active' => true,
                'requires_signature' => true,
                'insurance_available' => true,
                'max_weight' => 20.00,
                'sort_order' => 2
            ],
            [
                'name' => 'Giao Hàng Trong Ngày',
                'code' => 'same_day',
                'description' => 'Giao hàng trong cùng ngày (chỉ áp dụng tại TP.HCM và Hà Nội)',
                'base_cost' => 80000,
                'cost_per_kg' => 10000,
                'free_shipping_threshold' => 2000000,
                'min_delivery_days' => 0,
                'max_delivery_days' => 1,
                'available_countries' => json_encode(['VN']),
                'available_regions' => json_encode(['TP.HCM', 'Hà Nội']),
                'is_active' => true,
                'requires_signature' => true,
                'insurance_available' => true,
                'max_weight' => 10.00,
                'sort_order' => 3
            ],
            [
                'name' => 'Nhận Tại Cửa Hàng',
                'code' => 'pickup',
                'description' => 'Khách hàng đến nhận tại cửa hàng',
                'base_cost' => 0,
                'cost_per_kg' => 0,
                'free_shipping_threshold' => 0,
                'min_delivery_days' => 0,
                'max_delivery_days' => 1,
                'available_countries' => json_encode(['VN']),
                'is_active' => true,
                'requires_signature' => false,
                'insurance_available' => false,
                'max_weight' => 999.00,
                'sort_order' => 4
            ]
        ];

        foreach ($shippingMethods as $method) {
            DB::table('shipping_methods')->updateOrInsert(
                ['code' => $method['code']],
                array_merge($method, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
