@extends('admin.layouts.simple')

@section('title', 'Manufacturers')
@section('page_title', 'Manufacturers Management')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Manufacturers List</h3>
                    <div class="card-tools">
                        <a href="#" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Manufacturer
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Logo</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Website</th>
                                    <th>Products</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($manufacturers as $manufacturer)
                                <tr>
                                    <td>{{ $manufacturer->id_manufacturer }}</td>
                                    <td>
                                        @if($manufacturer->logo)
                                            <img src="{{ asset('storage/' . $manufacturer->logo) }}" 
                                                 alt="{{ $manufacturer->name_manufacturer }}" 
                                                 style="width: 50px; height: 50px; object-fit: cover;" class="img-thumbnail">
                                        @elseif($manufacturer->image_manufacturer)
                                            <img src="{{ asset('uploads/manufacturerimage/' . $manufacturer->image_manufacturer) }}" 
                                                 alt="{{ $manufacturer->name_manufacturer }}" 
                                                 style="width: 50px; height: 50px; object-fit: cover;" class="img-thumbnail">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-industry text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $manufacturer->name_manufacturer }}</strong>
                                        @if($manufacturer->slug)
                                            <br><small class="text-muted">{{ $manufacturer->slug }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $manufacturer->email ?? 'N/A' }}</td>
                                    <td>
                                        @if($manufacturer->website_url)
                                            <a href="{{ $manufacturer->website_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ $manufacturer->product_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        @if($manufacturer->is_active ?? true)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info">View</a>
                                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                                        <a href="#" class="btn btn-sm btn-danger">Delete</a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center">No manufacturers found</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($manufacturers->hasPages())
                <div class="card-footer">
                    {{ $manufacturers->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
@endsection
