<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sitemaps', function (Blueprint $table) {
            $table->id();
            $table->string('url');
            $table->string('type'); // product, category, page, blog
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the referenced model
            $table->decimal('priority', 2, 1)->default(0.5); // 0.0 to 1.0
            $table->enum('changefreq', ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'])
                  ->default('weekly');
            $table->timestamp('lastmod')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['type', 'is_active']);
            $table->index(['is_active', 'lastmod']);
            $table->unique(['url', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sitemaps');
    }
};
