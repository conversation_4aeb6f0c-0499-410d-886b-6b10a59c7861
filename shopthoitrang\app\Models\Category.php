<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $primaryKey = 'id_category';

    protected $fillable = [
        'name_category',
        'parent_id',
        'sort_order',
        'is_active',
        'slug',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'description',
        'image',
        'banner_image',
        'icon',
        'show_in_menu',
        'featured',
        'template'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'show_in_menu' => 'boolean',
        'featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id_category');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id_category')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'id_category', 'id_category');
    }

    public function activeProducts()
    {
        return $this->hasMany(Product::class, 'id_category', 'id_category')
                    ->where('status', 'active');
    }

    public function seoMetadata()
    {
        return $this->morphOne(SeoMetadata::class, 'seoable');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeRootCategories($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeWithChildren($query)
    {
        return $query->with(['children' => function($q) {
            $q->active()->orderBy('sort_order');
        }]);
    }

    // Accessors & Mutators
    public function getFullNameAttribute()
    {
        $names = collect([$this->name_category]);
        $parent = $this->parent;

        while ($parent) {
            $names->prepend($parent->name_category);
            $parent = $parent->parent;
        }

        return $names->implode(' > ');
    }

    public function getUrlAttribute()
    {
        return route('category.show', $this->slug);
    }

    // Helper Methods
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    public function getProductCount()
    {
        return $this->activeProducts()->count();
    }

    public function getAllDescendants()
    {
        $descendants = collect();

        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }

        return $descendants;
    }

    public static function getHierarchy()
    {
        return static::with('children')
                    ->rootCategories()
                    ->active()
                    ->orderBy('sort_order')
                    ->get();
    }

    public static function getFeaturedCategories($limit = 6)
    {
        return static::featured()
                    ->active()
                    ->orderBy('sort_order')
                    ->limit($limit)
                    ->get();
    }
}
