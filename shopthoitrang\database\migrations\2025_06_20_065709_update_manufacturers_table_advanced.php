<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            // SEO fields
            $table->string('slug')->unique()->after('name_manufacturer');
            $table->string('meta_title')->nullable()->after('slug');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');

            // Brand information
            $table->text('description')->nullable()->after('meta_keywords');
            $table->string('logo')->nullable()->after('description');
            $table->string('banner_image')->nullable()->after('logo');
            $table->string('website_url')->nullable()->after('banner_image');
            $table->string('email')->nullable()->after('website_url');
            $table->string('phone')->nullable()->after('email');

            // Address information
            $table->text('address')->nullable()->after('phone');
            $table->string('city')->nullable()->after('address');
            $table->string('country', 2)->default('VN')->after('city');

            // Social media links
            $table->string('facebook_url')->nullable()->after('country');
            $table->string('instagram_url')->nullable()->after('facebook_url');
            $table->string('twitter_url')->nullable()->after('instagram_url');
            $table->string('youtube_url')->nullable()->after('twitter_url');

            // Status and settings
            $table->boolean('is_active')->default(true)->after('youtube_url');
            $table->boolean('is_featured')->default(false)->after('is_active');
            $table->integer('sort_order')->default(0)->after('is_featured');

            // Statistics
            $table->integer('product_count')->default(0)->after('sort_order');

            // Indexes
            $table->index(['is_active', 'is_featured']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->dropColumn([
                'slug', 'meta_title', 'meta_description', 'meta_keywords', 'description',
                'logo', 'banner_image', 'website_url', 'email', 'phone', 'address',
                'city', 'country', 'facebook_url', 'instagram_url', 'twitter_url',
                'youtube_url', 'is_active', 'is_featured', 'sort_order', 'product_count'
            ]);
        });
    }
};
