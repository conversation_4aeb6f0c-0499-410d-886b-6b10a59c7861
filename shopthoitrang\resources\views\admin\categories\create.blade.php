@extends('admin.layouts.app')

@section('title', 'Thêm Danh mục mới')
@section('page_title', 'Thêm Danh mục mới')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.categories.index') }}">Danh mục</a></li>
    <li class="breadcrumb-item active">Thêm mới</li>
@endsection

@section('content')
    <form action="{{ route('admin.categories.store') }}" method="POST" enctype="multipart/form-data" id="categoryForm">
        @csrf
        
        <div class="row">
            <!-- Main Information -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-1"></i>
                            Thông tin cơ bản
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Category Name -->
                        <div class="form-group">
                            <label for="name_category">Tên danh mục <span class="text-danger">*</span></label>
                            <input type="text" name="name_category" id="name_category" 
                                   class="form-control @error('name_category') is-invalid @enderror" 
                                   value="{{ old('name_category') }}" required>
                            @error('name_category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div class="form-group">
                            <label for="slug">Slug <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" name="slug" id="slug" 
                                       class="form-control @error('slug') is-invalid @enderror" 
                                       value="{{ old('slug') }}" required>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="generateSlug">
                                        <i class="fas fa-magic"></i> Tự động
                                    </button>
                                </div>
                            </div>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">URL thân thiện cho danh mục này</small>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description">Mô tả</label>
                            <textarea name="description" id="description" rows="4" 
                                      class="form-control @error('description') is-invalid @enderror" 
                                      placeholder="Mô tả chi tiết về danh mục...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Icon -->
                        <div class="form-group">
                            <label for="icon">Icon (Font Awesome)</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i id="icon-preview" class="{{ old('icon', 'fas fa-folder') }}"></i>
                                    </span>
                                </div>
                                <input type="text" name="icon" id="icon" 
                                       class="form-control @error('icon') is-invalid @enderror" 
                                       value="{{ old('icon') }}" placeholder="fas fa-folder">
                            </div>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Ví dụ: fas fa-tshirt, fas fa-shoe-prints</small>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-search mr-1"></i>
                            Cài đặt SEO
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Meta Title -->
                        <div class="form-group">
                            <label for="meta_title">Meta Title</label>
                            <input type="text" name="meta_title" id="meta_title" 
                                   class="form-control @error('meta_title') is-invalid @enderror" 
                                   value="{{ old('meta_title') }}" maxlength="255">
                            @error('meta_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Tiêu đề hiển thị trên kết quả tìm kiếm (tối đa 255 ký tự)</small>
                        </div>

                        <!-- Meta Description -->
                        <div class="form-group">
                            <label for="meta_description">Meta Description</label>
                            <textarea name="meta_description" id="meta_description" rows="3" 
                                      class="form-control @error('meta_description') is-invalid @enderror" 
                                      maxlength="500" placeholder="Mô tả ngắn gọn về danh mục này...">{{ old('meta_description') }}</textarea>
                            @error('meta_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Mô tả hiển thị trên kết quả tìm kiếm (tối đa 500 ký tự)</small>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="form-group">
                            <label for="meta_keywords">Meta Keywords</label>
                            <input type="text" name="meta_keywords" id="meta_keywords" 
                                   class="form-control @error('meta_keywords') is-invalid @enderror" 
                                   value="{{ old('meta_keywords') }}" placeholder="từ khóa 1, từ khóa 2, từ khóa 3">
                            @error('meta_keywords')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Các từ khóa liên quan, cách nhau bằng dấu phẩy</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Publish Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog mr-1"></i>
                            Cài đặt
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Parent Category -->
                        <div class="form-group">
                            <label for="parent_id">Danh mục cha</label>
                            <select name="parent_id" id="parent_id" class="form-control select2">
                                <option value="">-- Danh mục gốc --</option>
                                @foreach($parentCategories as $parent)
                                    <option value="{{ $parent->id_category }}" {{ old('parent_id') == $parent->id_category ? 'selected' : '' }}>
                                        {{ $parent->name_category }}
                                    </option>
                                @endforeach
                            </select>
                            @error('parent_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Sort Order -->
                        <div class="form-group">
                            <label for="sort_order">Thứ tự sắp xếp</label>
                            <input type="number" name="sort_order" id="sort_order" 
                                   class="form-control @error('sort_order') is-invalid @enderror" 
                                   value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Số thứ tự hiển thị (0 = đầu tiên)</small>
                        </div>

                        <!-- Status Checkboxes -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" 
                                       name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">Kích hoạt</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="show_in_menu" 
                                       name="show_in_menu" value="1" {{ old('show_in_menu', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="show_in_menu">Hiển thị trong menu</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="featured" 
                                       name="featured" value="1" {{ old('featured') ? 'checked' : '' }}>
                                <label class="custom-control-label" for="featured">Danh mục nổi bật</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-image mr-1"></i>
                            Hình ảnh
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Category Image -->
                        <div class="form-group">
                            <label for="image">Hình ảnh danh mục</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="image" name="image" accept="image/*">
                                <label class="custom-file-label" for="image">Chọn hình ảnh...</label>
                            </div>
                            @error('image')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Định dạng: JPG, PNG, GIF. Tối đa 2MB</small>
                            
                            <!-- Image Preview -->
                            <div id="image-preview" class="mt-2" style="display: none;">
                                <img id="preview-img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                            </div>
                        </div>

                        <!-- Banner Image -->
                        <div class="form-group">
                            <label for="banner_image">Banner danh mục</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="banner_image" name="banner_image" accept="image/*">
                                <label class="custom-file-label" for="banner_image">Chọn banner...</label>
                            </div>
                            @error('banner_image')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Banner hiển thị trên trang danh mục. Tối đa 5MB</small>
                            
                            <!-- Banner Preview -->
                            <div id="banner-preview" class="mt-2" style="display: none;">
                                <img id="preview-banner" src="" alt="Banner Preview" class="img-thumbnail" style="max-width: 200px;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save"></i> Lưu danh mục
                        </button>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary btn-block">
                            <i class="fas fa-times"></i> Hủy bỏ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate slug from category name
    $('#name_category').on('input', function() {
        const name = $(this).val();
        if (name && !$('#slug').data('manual-edit')) {
            generateSlugFromName(name);
        }
    });

    // Manual slug generation
    $('#generateSlug').on('click', function() {
        const name = $('#name_category').val();
        if (name) {
            generateSlugFromName(name);
        }
    });

    // Mark slug as manually edited
    $('#slug').on('input', function() {
        $(this).data('manual-edit', true);
    });

    // Icon preview
    $('#icon').on('input', function() {
        const iconClass = $(this).val() || 'fas fa-folder';
        $('#icon-preview').attr('class', iconClass);
    });

    // Image preview
    $('#image').on('change', function() {
        previewImage(this, '#preview-img', '#image-preview');
    });

    $('#banner_image').on('change', function() {
        previewImage(this, '#preview-banner', '#banner-preview');
    });

    // Custom file input labels
    $('.custom-file-input').on('change', function() {
        const fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });

    function generateSlugFromName(name) {
        $.post('{{ route("admin.categories.generate-slug") }}', {
            name: name,
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(data) {
            $('#slug').val(data.slug);
        })
        .fail(function() {
            // Fallback: simple slug generation
            const slug = name.toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
            $('#slug').val(slug);
        });
    }

    function previewImage(input, imgSelector, containerSelector) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $(imgSelector).attr('src', e.target.result);
                $(containerSelector).show();
            };
            reader.readAsDataURL(input.files[0]);
        }
    }
});
</script>
@endpush
