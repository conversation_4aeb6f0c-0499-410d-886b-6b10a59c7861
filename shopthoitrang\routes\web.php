<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\User\ManufacturerControllerUser;
use App\Http\Controllers\Admin\ManufacturerController;
use App\Http\Controllers\User\ProductControllerUser;
use App\Http\Controllers\User\HomeController;
use App\Http\Controllers\User\CartController;
use App\Http\Controllers\User\OrderController;
use App\Http\Controllers\User\PaymentController;
use App\Http\Controllers\User\DetailsOrderController;
use App\Http\Controllers\Admin\AdminOrderController;
use App\Http\Controllers\Admin\PostController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('dashboard', [CategoryController::class, 'dashboard']);

Route::get('dashboard', [CategoryController::class, 'dashboard']);

Route::get('category', [CategoryController::class, 'indexCategory'])->name('category.index');
Route::get('category/create', [CategoryController::class, 'indexcreateCategory'])->name('category.create');
Route::post('category', [CategoryController::class, 'createCategory'])->name('category.store');
Route::get('categoryupdate', [CategoryController::class, 'indexupdateCategory'])->name('category.updateindex');
Route::post('categoryupdate', [CategoryController::class, 'updateCategory'])->name('category.updateCategory');
Route::delete('category/{id}', [CategoryController::class, 'deleteCategory'])->name('category.destroy');
Route::get('category/{id}/edit', [CategoryController::class, 'indexupdateCategory'])->name('category.edit');

//product
Route::get('listproduct', [ProductController::class, 'indexProduct'])->name('product.listproduct');
Route::get('addproduct', [ProductController::class, 'indexAddProduct'])->name('product.indexaddproduct');
Route::post('addproduct', [ProductController::class, 'addProduct'])->name('product.addproduct');
Route::get('deleteproduct', [ProductController::class, 'deleteProduct'])->name('product.deleteproduct');
Route::get('updateproduct', [ProductController::class, 'indexUpdateProduct'])->name('product.indexUpdateproduct');
Route::post('updateproduct', [ProductController::class, 'updateProduct'])->name('product.updateproduct');

// Register Client
Route::get('/register',[CustomerController::class,'indexRegister']);
Route::post('/register',[CustomerController::class,'authRegister'])->name('user.cus_register');

// Login client
Route::get('/login',[CustomerController::class,'indexLogin'])->name('user.indexlogin');
Route::post('/login',[CustomerController::class,'authLogin'])->name('user.cus_login');

// Logout
Route::get('/signout', [CustomerController::class, 'signOut'])->name('signout');

// List user Admin
Route::get('/listuser',[AdminUserController::class,'listUser'])->name('user.listuser');
//  Delete user admin
Route::get('deleteuser',[AdminUserController::class,'deleteUser'])->name('user.deleteUser');

// Update user admin
Route::get('/updateuser',[AdminUserController::class,'updateUser'])->name('user.updateUser');
Route::post('/updateuser',[AdminUserController::class,'postUpdateUser'])->name('user.postUpdateUser');

// Block/Unblock user admin
Route::post('/user/{id}/block', [AdminUserController::class, 'blockUser'])->name('user.block');
Route::post('/user/{id}/unblock', [AdminUserController::class, 'unblockUser'])->name('user.unblock');

// List_user  Search User
Route::get('/search',[AdminUserController::class,'searchUser'])->name('user.searchUser');

Route::get('/admin/dashboard', [AdminUserController::class, 'dashboard'])->name('admin.dashboard');

Route::get('/manufacture', [ManufacturerControllerUser::class, 'indexmanufacture'])->name('manufacture.indexmanufacture');
Route::get('/manufacturer/{id}', [ManufacturerControllerUser::class, 'showProductsByManufacturer'])->name('manufacturer.products');

//manufacturer
Route::get('listmanufacturer', [ManufacturerController::class, 'indexManufacturer'])->name('manufacturer.listmanufacturer');
Route::get('addmanufacturer', [ManufacturerController::class, 'indexAddManufacturer'])->name('manufacturer.addmanufacturer');
Route::post('addmanufacturer', [ManufacturerController::class, 'addManufacturer']);
Route::get('deletemanufacturer', [ManufacturerController::class, 'deleteManufacturer'])->name('manufacturer.deletemanufacturer');
Route::get('updatemanufacturer', [ManufacturerController::class, 'indexUpdateManufacturer'])->name('manufacturer.indexupdatemanufacturer');
Route::post('updatemanufacturer', [ManufacturerController::class, 'updateManufacturer'])->name('manufacturer.updatemanufacturer');

Route::get('detailproduct', [HomeController::class, 'indexDetailProduct'])->name('product.indexDetailproduct');

//search and filter
Route::get('/filterProduct', [ProductControllerUser::class, 'filterProduct'])->name('user.filterProduct');
Route::get('/searchProduct', [ProductControllerUser::class, 'searchProduct'])->name('user.searchProduct');

//home
Route::get('/Home', [HomeController::class, 'indexHome'])->name('home.index');

//add to cart
Route::post('addcard', [CartController::class, 'addCart'])->name('cart.addCard');
Route::get('mycard', [CartController::class, 'indexCard'])->name('cart.indexCart');
Route::post('mycard', [CartController::class, 'updateCart'])->name('cart.updateCart');
Route::get('deleteproductcard', [CartController::class, 'deleteProductCart'])->name('cart.deleteproductcart');
Route::get('cart/count', [CartController::class, 'getCount'])->name('cart.getCount');
Route::get('order/count', [OrderController::class, 'getCount'])->name('order.getCount');

//Bill Management
Route::get('orderindexAdmin',[AdminOrderController::class, 'orderindexAdmin'])->name('admin.orderindexAdmin');
Route::get('adminsearchorder',[AdminOrderController::class, 'adminSearchOrder'])->name('admin.adminSearchOrder');
Route::get('admindetailsorderindex',[AdminOrderController::class, 'adminDetailsOrderIndex'])->name('admin.adminDetailsOrderIndex');
Route::get('admindetailsorderdelete',[AdminOrderController::class, 'adminDetailsOrderDelete'])->name('admin.adminDetailsOrderDelete');

//payment
Route::get('myorder', [OrderController::class, 'addOrder'])->name('order.addOrder');
Route::post('myorder', [OrderController::class, 'addOrder'])->name('order.addOrder');
Route::get('payment', [PaymentController::class, 'paymentIndex'])->name('payment.paymentindex');
Route::get('detailsorder', [detailsOrderController::class, 'addDetailsOrder'])->name('detailsorder.addDetailsOrder');
Route::get('orderindex',[OrderController::class, 'orderIndex'])->name('order.orderIndex');
Route::get('detailsorderindex',[detailsOrderController::class, 'detailsOrderIndex'])->name('detailsorder.detailsOrderIndex');


//Post
Route::get('detailpost',[PostController::class, 'detailPost'])->name('post.detailpost');
Route::get('addpost',[PostController::class, 'indexAddPost'])->name('post.indexaddpost');
Route::post('addpost',[PostController::class, 'addPost'])->name('post.addpost');
Route::get('listpost',[PostController::class, 'listPost'])->name('post.listpost');
Route::get('listpostuser',[PostController::class, 'indexListPostUser'])->name('post.indexListPostUser');
Route::get('deletepost',[PostController::class, 'deletePost'])->name('post.deletepost');
Route::get('updatepost',[PostController::class, 'indexUpdatePost'])->name('post.indexupdatepost');
Route::post('updatepost',[PostController::class, 'updatePost'])->name('post.updatepost');
Route::post('searchpost',[PostController::class, 'searchPost'])->name('post.searchpost');

Route::get('deleteorder/{id_order}', [OrderController::class, 'deleteOrder'])->name('order.deleteOrder');

// Test loading screen
Route::get('test-loading', function() {
    return view('admin.test-loading');
})->name('admin.test-loading');

// New Admin Routes with SEO
Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\CategoryController::class);
    Route::post('categories/generate-slug', [App\Http\Controllers\Admin\CategoryController::class, 'generateSlug'])->name('categories.generate-slug');
    Route::post('categories/{category}/toggle-status', [App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::get('categories-hierarchy', [App\Http\Controllers\Admin\CategoryController::class, 'getHierarchy'])->name('categories.hierarchy');

    // Products Management
    Route::resource('products', App\Http\Controllers\Admin\ProductController::class);
    Route::post('products/generate-slug', [App\Http\Controllers\Admin\ProductController::class, 'generateSlug'])->name('products.generate-slug');
    Route::post('products/{product}/toggle-status', [App\Http\Controllers\Admin\ProductController::class, 'toggleStatus'])->name('products.toggle-status');
    Route::post('products/{product}/toggle-featured', [App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');

    // Manufacturers Management
    Route::resource('manufacturers', App\Http\Controllers\Admin\ManufacturerController::class);
    Route::post('manufacturers/generate-slug', [App\Http\Controllers\Admin\ManufacturerController::class, 'generateSlug'])->name('manufacturers.generate-slug');
    Route::post('manufacturers/{manufacturer}/toggle-status', [App\Http\Controllers\Admin\ManufacturerController::class, 'toggleStatus'])->name('manufacturers.toggle-status');

    // Orders Management
    Route::resource('orders', App\Http\Controllers\Admin\OrderController::class);
    Route::patch('orders/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::get('orders/{order}/invoice', [App\Http\Controllers\Admin\OrderController::class, 'invoice'])->name('orders.invoice');

    // Users Management
    Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    Route::post('users/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Reviews Management
    Route::resource('reviews', App\Http\Controllers\Admin\ReviewController::class);
    Route::post('reviews/{review}/approve', [App\Http\Controllers\Admin\ReviewController::class, 'approve'])->name('reviews.approve');
    Route::post('reviews/{review}/reject', [App\Http\Controllers\Admin\ReviewController::class, 'reject'])->name('reviews.reject');

    // SEO Management
    Route::prefix('seo')->name('seo.')->group(function () {
        Route::resource('metadata', App\Http\Controllers\Admin\SeoMetadataController::class);
        Route::resource('redirects', App\Http\Controllers\Admin\RedirectController::class);
        Route::get('sitemap', [App\Http\Controllers\Admin\SitemapController::class, 'index'])->name('sitemap.index');
        Route::post('sitemap/generate', [App\Http\Controllers\Admin\SitemapController::class, 'generate'])->name('sitemap.generate');
    });

    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('general', [App\Http\Controllers\Admin\SettingsController::class, 'general'])->name('general');
        Route::post('general', [App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('general.update');
        Route::get('shipping', [App\Http\Controllers\Admin\SettingsController::class, 'shipping'])->name('shipping');
        Route::post('shipping', [App\Http\Controllers\Admin\SettingsController::class, 'updateShipping'])->name('shipping.update');
        Route::get('payment', [App\Http\Controllers\Admin\SettingsController::class, 'payment'])->name('payment');
        Route::post('payment', [App\Http\Controllers\Admin\SettingsController::class, 'updatePayment'])->name('payment.update');
        Route::get('tax', [App\Http\Controllers\Admin\SettingsController::class, 'tax'])->name('tax');
        Route::post('tax', [App\Http\Controllers\Admin\SettingsController::class, 'updateTax'])->name('tax.update');
    });

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('sales', [App\Http\Controllers\Admin\ReportController::class, 'sales'])->name('sales');
        Route::get('products', [App\Http\Controllers\Admin\ReportController::class, 'products'])->name('products');
        Route::get('customers', [App\Http\Controllers\Admin\ReportController::class, 'customers'])->name('customers');
    });
});

// Home route
Route::get('/', [HomeController::class, 'indexHome'])->name('home');

// Test admin routes (temporary)
Route::get('/admin-test', function() {
    return view('admin.dashboard', [
        'stats' => [
            'total_products' => 150,
            'total_orders' => 89,
            'total_users' => 45,
            'total_revenue' => 15000000,
            'pending_orders' => 12,
            'completed_orders' => 77,
            'low_stock_products' => 5,
            'pending_reviews' => 8,
            'active_products' => 140,
            'daily_orders' => 3,
            'monthly_revenue' => 5000000
        ],
        'recentOrders' => [],
        'topProducts' => [],
        'salesChart' => []
    ]);
})->name('admin.test');

