<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\User\ManufacturerControllerUser;
use App\Http\Controllers\Admin\ManufacturerController;
use App\Http\Controllers\User\ProductControllerUser;
use App\Http\Controllers\User\HomeController;
use App\Http\Controllers\User\CartController;
use App\Http\Controllers\User\OrderController;
use App\Http\Controllers\User\PaymentController;
use App\Http\Controllers\User\DetailsOrderController;
use App\Http\Controllers\Admin\AdminOrderController;
use App\Http\Controllers\Admin\PostController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('dashboard', [CategoryController::class, 'dashboard']);

Route::get('dashboard', [CategoryController::class, 'dashboard']);

Route::get('category', [CategoryController::class, 'indexCategory'])->name('category.index');
Route::get('category/create', [CategoryController::class, 'indexcreateCategory'])->name('category.create');
Route::post('category', [CategoryController::class, 'createCategory'])->name('category.store');
Route::get('categoryupdate', [CategoryController::class, 'indexupdateCategory'])->name('category.updateindex');
Route::post('categoryupdate', [CategoryController::class, 'updateCategory'])->name('category.updateCategory');
Route::delete('category/{id}', [CategoryController::class, 'deleteCategory'])->name('category.destroy');
Route::get('category/{id}/edit', [CategoryController::class, 'indexupdateCategory'])->name('category.edit');

//product
Route::get('listproduct', [ProductController::class, 'indexProduct'])->name('product.listproduct');
Route::get('addproduct', [ProductController::class, 'indexAddProduct'])->name('product.indexaddproduct');
Route::post('addproduct', [ProductController::class, 'addProduct'])->name('product.addproduct');
Route::get('deleteproduct', [ProductController::class, 'deleteProduct'])->name('product.deleteproduct');
Route::get('updateproduct', [ProductController::class, 'indexUpdateProduct'])->name('product.indexUpdateproduct');
Route::post('updateproduct', [ProductController::class, 'updateProduct'])->name('product.updateproduct');

// Register Client
Route::get('/register',[CustomerController::class,'indexRegister']);
Route::post('/register',[CustomerController::class,'authRegister'])->name('user.cus_register');

// Login client
Route::get('/login',[CustomerController::class,'indexLogin'])->name('user.indexlogin');
Route::post('/login',[CustomerController::class,'authLogin'])->name('user.cus_login');

// Logout
Route::get('/signout', [CustomerController::class, 'signOut'])->name('signout');

// List user Admin
Route::get('/listuser',[AdminUserController::class,'listUser'])->name('user.listuser');
//  Delete user admin
Route::get('deleteuser',[AdminUserController::class,'deleteUser'])->name('user.deleteUser');

// Update user admin
Route::get('/updateuser',[AdminUserController::class,'updateUser'])->name('user.updateUser');
Route::post('/updateuser',[AdminUserController::class,'postUpdateUser'])->name('user.postUpdateUser');

// Block/Unblock user admin
Route::post('/user/{id}/block', [AdminUserController::class, 'blockUser'])->name('user.block');
Route::post('/user/{id}/unblock', [AdminUserController::class, 'unblockUser'])->name('user.unblock');

// List_user  Search User
Route::get('/search',[AdminUserController::class,'searchUser'])->name('user.searchUser');

Route::get('/admin/dashboard', [AdminUserController::class, 'dashboard'])->name('admin.dashboard');

Route::get('/manufacture', [ManufacturerControllerUser::class, 'indexmanufacture'])->name('manufacture.indexmanufacture');
Route::get('/manufacturer/{id}', [ManufacturerControllerUser::class, 'showProductsByManufacturer'])->name('manufacturer.products');

//manufacturer
Route::get('listmanufacturer', [ManufacturerController::class, 'indexManufacturer'])->name('manufacturer.listmanufacturer');
Route::get('addmanufacturer', [ManufacturerController::class, 'indexAddManufacturer'])->name('manufacturer.addmanufacturer');
Route::post('addmanufacturer', [ManufacturerController::class, 'addManufacturer']);
Route::get('deletemanufacturer', [ManufacturerController::class, 'deleteManufacturer'])->name('manufacturer.deletemanufacturer');
Route::get('updatemanufacturer', [ManufacturerController::class, 'indexUpdateManufacturer'])->name('manufacturer.indexupdatemanufacturer');
Route::post('updatemanufacturer', [ManufacturerController::class, 'updateManufacturer'])->name('manufacturer.updatemanufacturer');

Route::get('detailproduct', [HomeController::class, 'indexDetailProduct'])->name('product.indexDetailproduct');

//search and filter
Route::get('/filterProduct', [ProductControllerUser::class, 'filterProduct'])->name('user.filterProduct');
Route::get('/searchProduct', [ProductControllerUser::class, 'searchProduct'])->name('user.searchProduct');

//home
Route::get('/Home', [HomeController::class, 'indexHome'])->name('home.index');

//add to cart
Route::post('addcard', [CartController::class, 'addCart'])->name('cart.addCard');
Route::get('mycard', [CartController::class, 'indexCard'])->name('cart.indexCart');
Route::post('mycard', [CartController::class, 'updateCart'])->name('cart.updateCart');
Route::get('deleteproductcard', [CartController::class, 'deleteProductCart'])->name('cart.deleteproductcart');
Route::get('cart/count', [CartController::class, 'getCount'])->name('cart.getCount');
Route::get('order/count', [OrderController::class, 'getCount'])->name('order.getCount');

//Bill Management
Route::get('orderindexAdmin',[AdminOrderController::class, 'orderindexAdmin'])->name('admin.orderindexAdmin');
Route::get('adminsearchorder',[AdminOrderController::class, 'adminSearchOrder'])->name('admin.adminSearchOrder');
Route::get('admindetailsorderindex',[AdminOrderController::class, 'adminDetailsOrderIndex'])->name('admin.adminDetailsOrderIndex');
Route::get('admindetailsorderdelete',[AdminOrderController::class, 'adminDetailsOrderDelete'])->name('admin.adminDetailsOrderDelete');

//payment
Route::get('myorder', [OrderController::class, 'addOrder'])->name('order.addOrder');
Route::post('myorder', [OrderController::class, 'addOrder'])->name('order.addOrder');
Route::get('payment', [PaymentController::class, 'paymentIndex'])->name('payment.paymentindex');
Route::get('detailsorder', [detailsOrderController::class, 'addDetailsOrder'])->name('detailsorder.addDetailsOrder');
Route::get('orderindex',[OrderController::class, 'orderIndex'])->name('order.orderIndex');
Route::get('detailsorderindex',[detailsOrderController::class, 'detailsOrderIndex'])->name('detailsorder.detailsOrderIndex');


//Post
Route::get('detailpost',[PostController::class, 'detailPost'])->name('post.detailpost');
Route::get('addpost',[PostController::class, 'indexAddPost'])->name('post.indexaddpost');
Route::post('addpost',[PostController::class, 'addPost'])->name('post.addpost');
Route::get('listpost',[PostController::class, 'listPost'])->name('post.listpost');
Route::get('listpostuser',[PostController::class, 'indexListPostUser'])->name('post.indexListPostUser');
Route::get('deletepost',[PostController::class, 'deletePost'])->name('post.deletepost');
Route::get('updatepost',[PostController::class, 'indexUpdatePost'])->name('post.indexupdatepost');
Route::post('updatepost',[PostController::class, 'updatePost'])->name('post.updatepost');
Route::post('searchpost',[PostController::class, 'searchPost'])->name('post.searchpost');

Route::get('deleteorder/{id_order}', [OrderController::class, 'deleteOrder'])->name('order.deleteOrder');

// Test loading screen
Route::get('test-loading', function() {
    return view('admin.test-loading');
})->name('admin.test-loading');

// New Admin Routes with SEO
Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\CategoryController::class);
    Route::post('categories/generate-slug', [App\Http\Controllers\Admin\CategoryController::class, 'generateSlug'])->name('categories.generate-slug');
    Route::post('categories/{category}/toggle-status', [App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::get('categories-hierarchy', [App\Http\Controllers\Admin\CategoryController::class, 'getHierarchy'])->name('categories.hierarchy');

    // Products Management
    Route::resource('products', App\Http\Controllers\Admin\ProductController::class);
    Route::post('products/generate-slug', [App\Http\Controllers\Admin\ProductController::class, 'generateSlug'])->name('products.generate-slug');
    Route::post('products/{product}/toggle-status', [App\Http\Controllers\Admin\ProductController::class, 'toggleStatus'])->name('products.toggle-status');
    Route::post('products/{product}/toggle-featured', [App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');

    // Manufacturers Management
    Route::resource('manufacturers', App\Http\Controllers\Admin\ManufacturerController::class);
    Route::post('manufacturers/generate-slug', [App\Http\Controllers\Admin\ManufacturerController::class, 'generateSlug'])->name('manufacturers.generate-slug');
    Route::post('manufacturers/{manufacturer}/toggle-status', [App\Http\Controllers\Admin\ManufacturerController::class, 'toggleStatus'])->name('manufacturers.toggle-status');

    // Orders Management (commented out - controllers not created yet)
    // Route::resource('orders', App\Http\Controllers\Admin\OrderController::class);
    // Route::patch('orders/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.update-status');
    // Route::get('orders/{order}/invoice', [App\Http\Controllers\Admin\OrderController::class, 'invoice'])->name('orders.invoice');

    // Users Management (commented out - controllers not created yet)
    // Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    // Route::post('users/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Reviews Management (commented out - controllers not created yet)
    // Route::resource('reviews', App\Http\Controllers\Admin\ReviewController::class);
    // Route::post('reviews/{review}/approve', [App\Http\Controllers\Admin\ReviewController::class, 'approve'])->name('reviews.approve');
    // Route::post('reviews/{review}/reject', [App\Http\Controllers\Admin\ReviewController::class, 'reject'])->name('reviews.reject');

    // SEO Management (commented out - controllers not created yet)
    // Route::prefix('seo')->name('seo.')->group(function () {
    //     Route::resource('metadata', App\Http\Controllers\Admin\SeoMetadataController::class);
    //     Route::resource('redirects', App\Http\Controllers\Admin\RedirectController::class);
    //     Route::get('sitemap', [App\Http\Controllers\Admin\SitemapController::class, 'index'])->name('sitemap.index');
    //     Route::post('sitemap/generate', [App\Http\Controllers\Admin\SitemapController::class, 'generate'])->name('sitemap.generate');
    // });

    // Settings (commented out - controllers not created yet)
    // Route::prefix('settings')->name('settings.')->group(function () {
    //     Route::get('general', [App\Http\Controllers\Admin\SettingsController::class, 'general'])->name('general');
    //     Route::post('general', [App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('general.update');
    //     Route::get('shipping', [App\Http\Controllers\Admin\SettingsController::class, 'shipping'])->name('shipping');
    //     Route::post('shipping', [App\Http\Controllers\Admin\SettingsController::class, 'updateShipping'])->name('shipping.update');
    //     Route::get('payment', [App\Http\Controllers\Admin\SettingsController::class, 'payment'])->name('payment');
    //     Route::post('payment', [App\Http\Controllers\Admin\SettingsController::class, 'updatePayment'])->name('payment.update');
    //     Route::get('tax', [App\Http\Controllers\Admin\SettingsController::class, 'tax'])->name('tax');
    //     Route::post('tax', [App\Http\Controllers\Admin\SettingsController::class, 'updateTax'])->name('tax.update');
    // });

    // Reports (commented out - controllers not created yet)
    // Route::prefix('reports')->name('reports.')->group(function () {
    //     Route::get('sales', [App\Http\Controllers\Admin\ReportController::class, 'sales'])->name('sales');
    //     Route::get('products', [App\Http\Controllers\Admin\ReportController::class, 'products'])->name('products');
    //     Route::get('customers', [App\Http\Controllers\Admin\ReportController::class, 'customers'])->name('customers');
    // });
});

// Home route
Route::get('/', [HomeController::class, 'indexHome'])->name('home');

// Test routes (temporary)
Route::get('/test', function() {
    return '<h1>Test Route Works!</h1><p>Laravel is running correctly.</p>';
});

Route::get('/admin-test', function() {
    try {
        return view('admin.dashboard', [
            'stats' => [
                'total_products' => 150,
                'total_orders' => 89,
                'total_users' => 45,
                'total_revenue' => 15000000,
                'pending_orders' => 12,
                'completed_orders' => 77,
                'low_stock_products' => 5,
                'pending_reviews' => 8,
                'active_products' => 140,
                'daily_orders' => 3,
                'monthly_revenue' => 5000000
            ],
            'recentOrders' => [],
            'topProducts' => [],
            'salesChart' => []
        ]);
    } catch (\Exception $e) {
        return '<h1>Error in Admin Test</h1><p>' . $e->getMessage() . '</p><pre>' . $e->getTraceAsString() . '</pre>';
    }
})->name('admin.test');

// Simple admin dashboard test
Route::get('/admin-simple', function() {
    return '<!DOCTYPE html>
<html>
<head>
    <title>Admin Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <div class="content-wrapper" style="margin-left: 0;">
            <section class="content">
                <div class="container-fluid">
                    <h1>Admin Interface Test</h1>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>150</h3>
                                    <p>Products</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</body>
</html>';
});

// Test categories
Route::get('/test-categories', function() {
    try {
        $categories = \App\Models\Category::with(['parent', 'children'])->paginate(15);
        $parentCategories = \App\Models\Category::whereNull('parent_id')->get();
        return view('admin.categories.index', compact('categories', 'parentCategories'));
    } catch (\Exception $e) {
        return '<h1>Error in Categories</h1><p>' . $e->getMessage() . '</p><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

// Test manufacturers
Route::get('/test-manufacturers', function() {
    try {
        $manufacturers = \App\Models\Manufacturer::paginate(15);
        return view('admin.manufacturers.index', compact('manufacturers'));
    } catch (\Exception $e) {
        return '<h1>Error in Manufacturers</h1><p>' . $e->getMessage() . '</p><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

// Test products
Route::get('/test-products', function() {
    try {
        $products = \App\Models\Product::with(['category', 'manufacturer'])->paginate(15);
        $categories = \App\Models\Category::all();
        $manufacturers = \App\Models\Manufacturer::all();

        return view('admin.products.index', compact('products', 'categories', 'manufacturers'));
    } catch (\Exception $e) {
        return '<h1>Error in Products</h1><p>' . $e->getMessage() . '</p><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

// Debug routes
Route::get('/debug-db', function() {
    try {
        $categories = \App\Models\Category::count();
        $products = \App\Models\Product::count();
        $manufacturers = \App\Models\Manufacturer::count();

        return "Database Connection OK!<br>
                Categories: {$categories}<br>
                Products: {$products}<br>
                Manufacturers: {$manufacturers}";
    } catch (\Exception $e) {
        return 'Database Error: ' . $e->getMessage();
    }
});

Route::get('/debug-view', function() {
    try {
        return view('admin.layouts.app', [
            'content' => '<h1>Test Content</h1>'
        ]);
    } catch (\Exception $e) {
        return 'View Error: ' . $e->getMessage() . '<br><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

// Simple admin routes
Route::get('/admin-categories', function() {
    try {
        $categories = \App\Models\Category::with(['parent'])->paginate(15);
        return view('admin.categories.simple', compact('categories'));
    } catch (\Exception $e) {
        return 'Categories Error: ' . $e->getMessage() . '<br><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

Route::get('/admin-products', function() {
    try {
        $products = \App\Models\Product::with(['category', 'manufacturer'])->paginate(15);
        return view('admin.products.simple', compact('products'));
    } catch (\Exception $e) {
        return 'Products Error: ' . $e->getMessage() . '<br><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

Route::get('/admin-manufacturers', function() {
    try {
        $manufacturers = \App\Models\Manufacturer::paginate(15);
        return view('admin.manufacturers.simple', compact('manufacturers'));
    } catch (\Exception $e) {
        return 'Manufacturers Error: ' . $e->getMessage() . '<br><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

// Simple admin dashboard
Route::get('/admin-dashboard', function() {
    try {
        $stats = [
            'total_products' => \App\Models\Product::count(),
            'total_categories' => \App\Models\Category::count(),
            'total_manufacturers' => \App\Models\Manufacturer::count(),
            'total_revenue' => 15000000
        ];

        $recentProducts = \App\Models\Product::latest()->limit(5)->get();
        $topCategories = \App\Models\Category::withCount('products')->limit(5)->get();

        return view('admin.dashboard-simple', compact('stats', 'recentProducts', 'topCategories'));
    } catch (\Exception $e) {
        return 'Dashboard Error: ' . $e->getMessage() . '<br><pre>' . $e->getTraceAsString() . '</pre>';
    }
});

// Main admin entry point
Route::get('/admin', function() {
    return redirect('/admin-dashboard');
});

// Admin index page
Route::get('/admin-index', function() {
    return '<!DOCTYPE html>
<html>
<head>
    <title>Admin Panel - Shop Thời Trang</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .admin-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-admin { border-radius: 25px; padding: 12px 30px; margin: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-8">
                <div class="admin-card p-5 text-center">
                    <h1 class="mb-4"><i class="fas fa-cogs text-primary"></i> Admin Panel</h1>
                    <h3 class="text-muted mb-5">Shop Thời Trang Management System</h3>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="/admin-dashboard" class="btn btn-primary btn-admin btn-block">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/admin-categories" class="btn btn-info btn-admin btn-block">
                                <i class="fas fa-list"></i> Categories
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/admin-products" class="btn btn-success btn-admin btn-block">
                                <i class="fas fa-box"></i> Products
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/admin-manufacturers" class="btn btn-warning btn-admin btn-block">
                                <i class="fas fa-industry"></i> Manufacturers
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/debug-db" class="btn btn-secondary btn-admin btn-block">
                                <i class="fas fa-database"></i> Database Status
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/" class="btn btn-outline-primary btn-admin btn-block">
                                <i class="fas fa-home"></i> Back to Website
                            </a>
                        </div>
                    </div>

                    <hr class="my-4">
                    <p class="text-muted">
                        <i class="fas fa-check-circle text-success"></i>
                        Admin interface is working correctly with AdminLTE & SEO features
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
});

