<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductAttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $attributes = [
            [
                'name' => '<PERSON>ích <PERSON>',
                'slug' => 'kich-thuoc',
                'type' => 'select',
                'options' => json_encode(['XS', 'S', 'M', 'L', 'XL', 'XXL']),
                'is_required' => true,
                'is_filterable' => true,
                'is_comparable' => true,
                'is_visible_on_front' => true,
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'name' => '<PERSON><PERSON>u <PERSON>',
                'slug' => 'mau-sac',
                'type' => 'select',
                'options' => json_encode(['Đen', 'Trắng', 'Xám', 'Đỏ', 'Xanh Navy', 'Xanh <PERSON>', 'Hồng', 'Vàng', 'Nâu', 'Be']),
                'is_required' => true,
                'is_filterable' => true,
                'is_comparable' => true,
                'is_visible_on_front' => true,
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'name' => 'Chất Liệu',
                'slug' => 'chat-lieu',
                'type' => 'select',
                'options' => json_encode(['Cotton', 'Polyester', 'Linen', 'Silk', 'Wool', 'Denim', 'Leather', 'Synthetic']),
                'is_required' => false,
                'is_filterable' => true,
                'is_comparable' => true,
                'is_visible_on_front' => true,
                'sort_order' => 3,
                'is_active' => true
            ],
            [
                'name' => 'Phong Cách',
                'slug' => 'phong-cach',
                'type' => 'select',
                'options' => json_encode(['Casual', 'Formal', 'Sport', 'Vintage', 'Modern', 'Classic']),
                'is_required' => false,
                'is_filterable' => true,
                'is_comparable' => false,
                'is_visible_on_front' => true,
                'sort_order' => 4,
                'is_active' => true
            ],
            [
                'name' => 'Giới Tính',
                'slug' => 'gioi-tinh',
                'type' => 'select',
                'options' => json_encode(['Nam', 'Nữ', 'Unisex']),
                'is_required' => true,
                'is_filterable' => true,
                'is_comparable' => false,
                'is_visible_on_front' => true,
                'sort_order' => 5,
                'is_active' => true
            ]
        ];

        foreach ($attributes as $attribute) {
            DB::table('product_attributes')->updateOrInsert(
                ['slug' => $attribute['slug']],
                array_merge($attribute, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
