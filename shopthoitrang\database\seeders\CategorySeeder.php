<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            // Main categories
            [
                'name_category' => 'Thời Trang Nam',
                'parent_id' => null,
                'slug' => 'thoi-trang-nam',
                'meta_title' => 'Thời Trang Nam - Quần Áo <PERSON> Đẹp, Chất Lượng <PERSON>',
                'meta_description' => 'Khám phá bộ sưu tập thời trang nam đa dạng với áo sơ mi, quần jeans, áo thun và nhiều sản phẩm khác. Chất lượng cao, giá cả hợp lý.',
                'meta_keywords' => 'thời trang nam, quần áo nam, áo sơ mi nam, quần jeans nam',
                'description' => '<PERSON><PERSON> sưu tập thời trang nam hiện đại với đa dạng sản phẩm từ casual đến formal.',
                'image' => '/images/categories/thoi-trang-nam.jpg',
                'is_active' => true,
                'featured' => true,
                'sort_order' => 1,
                'show_in_menu' => true
            ],
            [
                'name_category' => 'Thời Trang Nữ',
                'parent_id' => null,
                'slug' => 'thoi-trang-nu',
                'meta_title' => 'Thời Trang Nữ - Quần Áo Nữ Đẹp, Xu Hướng Mới Nhất',
                'meta_description' => 'Cập nhật xu hướng thời trang nữ mới nhất với váy đầm, áo blouse, quần jeans và phụ kiện thời trang. Phong cách hiện đại, nữ tính.',
                'meta_keywords' => 'thời trang nữ, quần áo nữ, váy đầm, áo blouse',
                'description' => 'Bộ sưu tập thời trang nữ thanh lịch và hiện đại cho mọi dịp.',
                'image' => '/images/categories/thoi-trang-nu.jpg',
                'is_active' => true,
                'featured' => true,
                'sort_order' => 2,
                'show_in_menu' => true
            ],
            [
                'name_category' => 'Giày Dép',
                'parent_id' => null,
                'slug' => 'giay-dep',
                'meta_title' => 'Giày Dép Nam Nữ - Sneaker, Giày Cao Gót, Sandal',
                'meta_description' => 'Bộ sưu tập giày dép đa dạng cho nam và nữ. Sneaker thể thao, giày cao gót, sandal và nhiều kiểu dáng khác.',
                'meta_keywords' => 'giày dép, sneaker, giày cao gót, sandal',
                'description' => 'Đa dạng các loại giày dép thời trang cho mọi phong cách.',
                'image' => '/images/categories/giay-dep.jpg',
                'is_active' => true,
                'featured' => true,
                'sort_order' => 3,
                'show_in_menu' => true
            ],
            [
                'name_category' => 'Phụ Kiện',
                'parent_id' => null,
                'slug' => 'phu-kien',
                'meta_title' => 'Phụ Kiện Thời Trang - Túi Xách, Đồng Hồ, Trang Sức',
                'meta_description' => 'Hoàn thiện phong cách với bộ sưu tập phụ kiện thời trang: túi xách, đồng hồ, trang sức và nhiều món đồ khác.',
                'meta_keywords' => 'phụ kiện thời trang, túi xách, đồng hồ, trang sức',
                'description' => 'Phụ kiện thời trang để hoàn thiện phong cách của bạn.',
                'image' => '/images/categories/phu-kien.jpg',
                'is_active' => true,
                'featured' => false,
                'sort_order' => 4,
                'show_in_menu' => true
            ]
        ];

        // Insert main categories first and store their IDs
        $mainCategories = [];
        foreach ($categories as $category) {
            $categoryId = DB::table('categories')->insertGetId(array_merge($category, [
                'created_at' => now(),
                'updated_at' => now()
            ]));

            $mainCategories[$category['slug']] = $categoryId;
        }

        // Subcategories
        $subcategories = [
            // Nam subcategories
            [
                'name_category' => 'Áo Sơ Mi Nam',
                'parent_id' => $mainCategories['thoi-trang-nam'],
                'slug' => 'ao-so-mi-nam',
                'meta_title' => 'Áo Sơ Mi Nam - Formal, Casual, Chất Lượng Cao',
                'meta_description' => 'Bộ sưu tập áo sơ mi nam đa dạng từ formal đến casual. Chất liệu cao cấp, thiết kế hiện đại.',
                'meta_keywords' => 'áo sơ mi nam, áo sơ mi formal, áo sơ mi casual',
                'description' => 'Áo sơ mi nam chất lượng cao cho mọi dịp.',
                'is_active' => true,
                'sort_order' => 1,
                'show_in_menu' => true
            ],
            [
                'name_category' => 'Quần Jeans Nam',
                'parent_id' => $mainCategories['thoi-trang-nam'],
                'slug' => 'quan-jeans-nam',
                'meta_title' => 'Quần Jeans Nam - Skinny, Straight, Ripped',
                'meta_description' => 'Quần jeans nam đa dạng kiểu dáng: skinny, straight, ripped. Chất liệu denim cao cấp, bền đẹp.',
                'meta_keywords' => 'quần jeans nam, quần jean skinny, quần jean straight',
                'description' => 'Quần jeans nam với nhiều kiểu dáng thời trang.',
                'is_active' => true,
                'sort_order' => 2,
                'show_in_menu' => true
            ],
            // Nữ subcategories
            [
                'name_category' => 'Váy Đầm',
                'parent_id' => $mainCategories['thoi-trang-nu'],
                'slug' => 'vay-dam',
                'meta_title' => 'Váy Đầm Nữ - Đầm Dự Tiệc, Đầm Công Sở, Đầm Maxi',
                'meta_description' => 'Bộ sưu tập váy đầm nữ đa dạng: đầm dự tiệc, đầm công sở, đầm maxi. Thiết kế nữ tính, thanh lịch.',
                'meta_keywords' => 'váy đầm, đầm dự tiệc, đầm công sở, đầm maxi',
                'description' => 'Váy đầm nữ tính và thanh lịch cho mọi dịp.',
                'is_active' => true,
                'sort_order' => 1,
                'show_in_menu' => true
            ],
            [
                'name_category' => 'Áo Blouse',
                'parent_id' => $mainCategories['thoi-trang-nu'],
                'slug' => 'ao-blouse',
                'meta_title' => 'Áo Blouse Nữ - Công Sở, Dự Tiệc, Casual',
                'meta_description' => 'Áo blouse nữ đa dạng phong cách từ công sở đến dự tiệc. Chất liệu mềm mại, thiết kế tinh tế.',
                'meta_keywords' => 'áo blouse, áo blouse công sở, áo blouse nữ',
                'description' => 'Áo blouse nữ thanh lịch và hiện đại.',
                'is_active' => true,
                'sort_order' => 2,
                'show_in_menu' => true
            ]
        ];

        // Insert subcategories
        foreach ($subcategories as $subcategory) {
            DB::table('categories')->insert(array_merge($subcategory, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
