<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // SEO fields
            $table->string('slug')->unique()->after('name_product');
            $table->string('meta_title')->nullable()->after('slug');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');

            // Product status and visibility
            $table->enum('status', ['active', 'inactive', 'draft', 'out_of_stock'])->default('active')->after('meta_keywords');
            $table->boolean('is_featured')->default(false)->after('status');
            $table->boolean('is_digital')->default(false)->after('is_featured');

            // Pricing
            $table->decimal('compare_price', 10, 2)->nullable()->after('price_product');
            $table->decimal('cost_price', 10, 2)->nullable()->after('compare_price');
            $table->boolean('track_inventory')->default(true)->after('cost_price');

            // Inventory management
            $table->integer('min_stock_level')->default(0)->after('quantity_product');
            $table->integer('max_stock_level')->nullable()->after('min_stock_level');
            $table->boolean('allow_backorder')->default(false)->after('max_stock_level');

            // Product dimensions and shipping
            $table->decimal('weight', 8, 2)->nullable()->after('allow_backorder');
            $table->decimal('length', 8, 2)->nullable()->after('weight');
            $table->decimal('width', 8, 2)->nullable()->after('length');
            $table->decimal('height', 8, 2)->nullable()->after('width');
            $table->boolean('requires_shipping')->default(true)->after('height');

            // Product ratings and reviews
            $table->decimal('average_rating', 3, 2)->default(0)->after('purchased');
            $table->integer('review_count')->default(0)->after('average_rating');

            // Product tags and categories (additional)
            $table->json('tags')->nullable()->after('review_count');
            $table->text('short_description')->nullable()->after('tags');

            // Product variants support
            $table->boolean('has_variants')->default(false)->after('short_description');
            $table->json('variant_options')->nullable()->after('has_variants'); // For storing variant types like size, color

            // SEO and marketing
            $table->integer('view_count')->default(0)->after('variant_options');
            $table->timestamp('featured_until')->nullable()->after('view_count');

            // Indexes for better performance
            $table->index(['status', 'is_featured']);
            $table->index(['id_category', 'status']);
            $table->index(['id_manufacturer', 'status']);
            $table->index('average_rating');
            $table->index('view_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'slug', 'meta_title', 'meta_description', 'meta_keywords', 'status', 'is_featured',
                'is_digital', 'compare_price', 'cost_price', 'track_inventory', 'min_stock_level',
                'max_stock_level', 'allow_backorder', 'weight', 'length', 'width', 'height',
                'requires_shipping', 'average_rating', 'review_count', 'tags', 'short_description',
                'has_variants', 'variant_options', 'view_count', 'featured_until'
            ]);
        });
    }
};
